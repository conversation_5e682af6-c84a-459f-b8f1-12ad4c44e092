"""
Multi-Protocol Exploitation Engine
Coordinates exploitation across different network protocols and services
"""

import asyncio
from dataclasses import dataclass, field
from typing import List, Dict, Optional, Any, Union
from datetime import datetime
from enum import Enum

from loguru import logger
from src.core.config import ConfigManager


class ProtocolType(Enum):
    """Supported protocol types for exploitation"""
    SSH = "ssh"
    SMB = "smb"
    HTTP = "http"
    HTTPS = "https"
    DNS = "dns"
    FTP = "ftp"
    TELNET = "telnet"
    SNMP = "snmp"


class ExploitationMode(Enum):
    """Different exploitation modes"""
    BRUTE_FORCE = "brute_force"
    TARGETED = "targeted"
    STEALTH = "stealth"
    AUTOMATED = "automated"


@dataclass
class ExploitTarget:
    """Represents a target for exploitation"""
    host: str
    port: int
    protocol: ProtocolType
    service_name: Optional[str] = None
    service_version: Optional[str] = None
    os_info: Optional[str] = None
    additional_info: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ExploitResult:
    """Results from an exploitation attempt"""
    target: ExploitTarget
    success: bool
    exploit_type: str
    timestamp: datetime = field(default_factory=datetime.utcnow)
    credentials: Optional[Dict[str, str]] = None
    access_level: Optional[str] = None
    payload_delivered: bool = False
    persistence_established: bool = False
    error_message: Optional[str] = None
    execution_time: float = 0.0
    additional_data: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ExploitationConfig:
    """Configuration for exploitation attempts"""
    mode: ExploitationMode = ExploitationMode.AUTOMATED
    max_concurrent: int = 10
    timeout: int = 60
    retry_attempts: int = 3
    stealth_delay: float = 1.0
    wordlist_paths: Dict[str, str] = field(default_factory=dict)
    custom_payloads: Dict[str, str] = field(default_factory=dict)
    target_filters: Dict[str, Any] = field(default_factory=dict)


class MultiProtocolExploiter:
    """
    Main exploitation engine that coordinates attacks across multiple protocols
    """

    def __init__(self, config_manager: Optional[ConfigManager] = None):
        """Initialize the multi-protocol exploitation engine"""
        self.config_manager = config_manager or ConfigManager()
        self.config = self._load_exploitation_config()
        
        # Protocol-specific exploiters (lazy loaded)
        self._ssh_exploiter = None
        self._smb_exploiter = None
        self._dns_tunneler = None
        self._shellshock_exploiter = None
        
        # Results tracking
        self.results: List[ExploitResult] = []
        self.active_sessions: Dict[str, Any] = {}

    def _load_exploitation_config(self) -> ExploitationConfig:
        """Load exploitation configuration from config manager"""
        exploitation_config = self.config_manager.get_exploitation_config()
        
        return ExploitationConfig(
            max_concurrent=exploitation_config.max_concurrent,
            timeout=exploitation_config.timeout,
            retry_attempts=exploitation_config.retry_attempts,
            wordlist_paths={
                'users': self.config_manager.get('exploitation.ssh.wordlists.users', 'data/wordlists/users.txt'),
                'passwords': self.config_manager.get('exploitation.ssh.wordlists.passwords', 'data/wordlists/passwords.txt')
            }
        )

    def _get_ssh_exploiter(self):
        """Lazy load SSH exploiter"""
        if self._ssh_exploiter is None:
            from .ssh_exploiter import SSHExploiter
            self._ssh_exploiter = SSHExploiter(self.config_manager)
        return self._ssh_exploiter

    def _get_smb_exploiter(self):
        """Lazy load SMB exploiter"""
        if self._smb_exploiter is None:
            from .smb_exploiter import SMBExploiter
            self._smb_exploiter = SMBExploiter(self.config_manager)
        return self._smb_exploiter

    def _get_dns_tunneler(self):
        """Lazy load DNS tunneler"""
        if self._dns_tunneler is None:
            from .dns_tunneling import DNSTunneler
            self._dns_tunneler = DNSTunneler(self.config_manager)
        return self._dns_tunneler

    def _get_shellshock_exploiter(self):
        """Lazy load Shellshock exploiter"""
        if self._shellshock_exploiter is None:
            from .shellshock_exploiter import ShellshockExploiter
            self._shellshock_exploiter = ShellshockExploiter(self.config_manager)
        return self._shellshock_exploiter

    async def exploit_targets(self, targets: List[ExploitTarget], 
                            config: Optional[ExploitationConfig] = None) -> List[ExploitResult]:
        """
        Exploit multiple targets using appropriate protocol-specific methods
        
        Args:
            targets: List of targets to exploit
            config: Optional exploitation configuration
            
        Returns:
            List of exploitation results
        """
        config = config or self.config
        results = []
        
        logger.info(f"Starting exploitation of {len(targets)} targets")
        
        # Group targets by protocol for efficient processing
        protocol_groups = self._group_targets_by_protocol(targets)
        
        # Process each protocol group
        for protocol, protocol_targets in protocol_groups.items():
            logger.info(f"Exploiting {len(protocol_targets)} {protocol.value} targets")
            
            try:
                protocol_results = await self._exploit_protocol_group(protocol, protocol_targets, config)
                results.extend(protocol_results)
            except Exception as e:
                logger.error(f"Error exploiting {protocol.value} targets: {e}")
                
                # Create failed results for this protocol group
                for target in protocol_targets:
                    failed_result = ExploitResult(
                        target=target,
                        success=False,
                        exploit_type=f"{protocol.value}_exploitation",
                        error_message=str(e)
                    )
                    results.append(failed_result)
        
        self.results.extend(results)
        logger.info(f"Exploitation completed. {len([r for r in results if r.success])} successful, "
                   f"{len([r for r in results if not r.success])} failed")
        
        return results

    def _group_targets_by_protocol(self, targets: List[ExploitTarget]) -> Dict[ProtocolType, List[ExploitTarget]]:
        """Group targets by protocol type for efficient processing"""
        groups = {}
        
        for target in targets:
            if target.protocol not in groups:
                groups[target.protocol] = []
            groups[target.protocol].append(target)
        
        return groups

    async def _exploit_protocol_group(self, protocol: ProtocolType,
                                    targets: List[ExploitTarget],
                                    config: ExploitationConfig) -> List[ExploitResult]:
        """Exploit a group of targets using the same protocol"""
        if protocol == ProtocolType.SSH:
            return await self._get_ssh_exploiter().exploit_targets(targets, config)
        elif protocol == ProtocolType.SMB:
            return await self._get_smb_exploiter().exploit_targets(targets, config)
        elif protocol == ProtocolType.DNS:
            return await self._get_dns_tunneler().exploit_targets(targets, config)
        elif protocol in [ProtocolType.HTTP, ProtocolType.HTTPS]:
            return await self._get_shellshock_exploiter().exploit_targets(targets, config)
        else:
            logger.warning(f"No exploiter available for protocol: {protocol.value}")
            return []

    async def exploit_single_target(self, target: ExploitTarget,
                                  config: Optional[ExploitationConfig] = None) -> ExploitResult:
        """
        Exploit a single target
        
        Args:
            target: Target to exploit
            config: Optional exploitation configuration
            
        Returns:
            Exploitation result
        """
        results = await self.exploit_targets([target], config)
        return results[0] if results else ExploitResult(
            target=target,
            success=False,
            exploit_type="unknown",
            error_message="No exploiter available"
        )

    def create_target_from_scan_result(self, host_ip: str, port: int, 
                                     service_name: str, protocol: str = "tcp") -> Optional[ExploitTarget]:
        """
        Create an ExploitTarget from scan results
        
        Args:
            host_ip: Target host IP address
            port: Target port number
            service_name: Service name detected
            protocol: Network protocol (tcp/udp)
            
        Returns:
            ExploitTarget if service is exploitable, None otherwise
        """
        # Map service names to protocol types
        service_protocol_map = {
            'ssh': ProtocolType.SSH,
            'microsoft-ds': ProtocolType.SMB,
            'netbios-ssn': ProtocolType.SMB,
            'http': ProtocolType.HTTP,
            'https': ProtocolType.HTTPS,
            'domain': ProtocolType.DNS,
            'ftp': ProtocolType.FTP,
            'telnet': ProtocolType.TELNET,
            'snmp': ProtocolType.SNMP,
        }
        
        protocol_type = service_protocol_map.get(service_name.lower())
        if not protocol_type:
            logger.debug(f"No exploiter available for service: {service_name}")
            return None
        
        return ExploitTarget(
            host=host_ip,
            port=port,
            protocol=protocol_type,
            service_name=service_name
        )

    def get_exploitation_statistics(self) -> Dict[str, Any]:
        """Get statistics about exploitation attempts"""
        if not self.results:
            return {}
        
        total_attempts = len(self.results)
        successful = len([r for r in self.results if r.success])
        failed = total_attempts - successful
        
        # Group by protocol
        protocol_stats = {}
        for result in self.results:
            protocol = result.target.protocol.value
            if protocol not in protocol_stats:
                protocol_stats[protocol] = {'total': 0, 'successful': 0}
            
            protocol_stats[protocol]['total'] += 1
            if result.success:
                protocol_stats[protocol]['successful'] += 1
        
        return {
            'total_attempts': total_attempts,
            'successful': successful,
            'failed': failed,
            'success_rate': (successful / total_attempts) * 100 if total_attempts > 0 else 0,
            'protocol_breakdown': protocol_stats,
            'active_sessions': len(self.active_sessions)
        }

    def clear_results(self):
        """Clear stored results"""
        self.results.clear()
        logger.info("Exploitation results cleared")

    def get_successful_credentials(self) -> List[Dict[str, Any]]:
        """Get all successfully obtained credentials"""
        credentials = []
        
        for result in self.results:
            if result.success and result.credentials:
                credentials.append({
                    'target': f"{result.target.host}:{result.target.port}",
                    'protocol': result.target.protocol.value,
                    'credentials': result.credentials,
                    'access_level': result.access_level,
                    'timestamp': result.timestamp
                })
        
        return credentials

    # Properties for accessing individual exploiters
    @property
    def ssh_exploiter(self):
        """Access SSH exploiter"""
        return self._get_ssh_exploiter()

    @property
    def smb_exploiter(self):
        """Access SMB exploiter"""
        return self._get_smb_exploiter()

    @property
    def dns_tunneler(self):
        """Access DNS tunneler"""
        return self._get_dns_tunneler()

    @property
    def shellshock_exploiter(self):
        """Access Shellshock exploiter"""
        return self._get_shellshock_exploiter()
