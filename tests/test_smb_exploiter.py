"""
Tests for SMB Exploiter Module
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock, MagicMock, mock_open
from datetime import datetime, timezone

from src.exploits.smb_exploiter import (
    SMBExploiter, SMBCredentials, SMBSession, SMBVersion, SMBVulnerability
)
from src.exploits.multi_protocol_engine import (
    ExploitTarget, ExploitResult, ExploitationConfig, ExploitationMode, ProtocolType
)


class TestSMBExploiter:
    """Test cases for SMB exploiter"""

    def setup_method(self):
        """Setup test environment"""
        self.exploiter = SMBExploiter()

    @pytest.mark.asyncio
    async def test_exploit_targets_without_impacket(self):
        """Test exploitation when Impacket is not available"""
        targets = [
            ExploitTarget(host="*************", port=445, protocol=ProtocolType.SMB, service_name="microsoft-ds"),
            ExploitTarget(host="*************", port=445, protocol=ProtocolType.SMB, service_name="microsoft-ds")
        ]
        config = ExploitationConfig(mode=ExploitationMode.TARGETED, timeout=30)

        # Mock IMPACKET_AVAILABLE as False
        with patch('src.exploits.smb_exploiter.IMPACKET_AVAILABLE', False):
            results = await self.exploiter.exploit_targets(targets, config)

            assert len(results) == 2
            for result in results:
                assert result.success is False
                assert result.exploit_type == "smb_exploitation"
                assert "Impacket library not available" in result.error_message

    @pytest.mark.asyncio
    async def test_exploit_single_target_success_via_vulnerability(self):
        """Test successful exploitation via vulnerability detection"""
        target = ExploitTarget(host="*************", port=445, protocol=ProtocolType.SMB, service_name="microsoft-ds")
        config = ExploitationConfig(mode=ExploitationMode.TARGETED, timeout=30)

        # Mock vulnerability detection to return success
        with patch.object(self.exploiter, '_check_smb_vulnerabilities') as mock_vuln_check:
            mock_result = ExploitResult(
                target=target,
                success=True,
                exploit_type="eternalblue_ms17_010",
                additional_data={'vulnerability': 'MS17-010', 'method': 'EternalBlue'}
            )
            mock_vuln_check.return_value = mock_result

            result = await self.exploiter.exploit_single_target(target, config)

            assert result.success is True
            assert result.exploit_type == "eternalblue_ms17_010"
            assert 'vulnerability' in result.additional_data
            assert result.execution_time is not None

    @pytest.mark.asyncio
    async def test_exploit_single_target_success_via_credentials(self):
        """Test successful exploitation via credential attack"""
        target = ExploitTarget(host="*************", port=445, protocol=ProtocolType.SMB, service_name="microsoft-ds")
        config = ExploitationConfig(mode=ExploitationMode.TARGETED, timeout=30)

        # Mock vulnerability check to fail, credential attack to succeed
        with patch.object(self.exploiter, '_check_smb_vulnerabilities') as mock_vuln_check, \
             patch.object(self.exploiter, '_smb_credential_attack') as mock_cred_attack:
            
            mock_vuln_check.return_value = ExploitResult(
                target=target, success=False, exploit_type="smb_vulnerability_scan"
            )
            
            mock_cred_result = ExploitResult(
                target=target,
                success=True,
                exploit_type="smb_credentials",
                credentials={'username': 'admin', 'password': 'password'},
                access_level="user"
            )
            mock_cred_attack.return_value = mock_cred_result

            result = await self.exploiter.exploit_single_target(target, config)

            assert result.success is True
            assert result.exploit_type == "smb_credentials"
            assert 'username' in result.credentials

    @pytest.mark.asyncio
    async def test_exploit_single_target_failure(self):
        """Test failed exploitation when all methods fail"""
        target = ExploitTarget(host="*************", port=445, protocol=ProtocolType.SMB, service_name="microsoft-ds")
        config = ExploitationConfig(mode=ExploitationMode.TARGETED, timeout=30)

        # Mock both vulnerability and credential attacks to fail
        with patch.object(self.exploiter, '_check_smb_vulnerabilities') as mock_vuln_check, \
             patch.object(self.exploiter, '_smb_credential_attack') as mock_cred_attack:
            
            mock_vuln_check.return_value = ExploitResult(
                target=target, success=False, exploit_type="smb_vulnerability_scan"
            )
            mock_cred_attack.return_value = ExploitResult(
                target=target, success=False, exploit_type="smb_credentials"
            )

            result = await self.exploiter.exploit_single_target(target, config)

            assert result.success is False
            assert result.exploit_type == "smb_exploitation"
            assert "All SMB exploitation methods failed" in result.error_message

    @pytest.mark.asyncio
    async def test_eternalblue_detection_success(self):
        """Test successful EternalBlue vulnerability detection"""
        target = ExploitTarget(host="*************", port=445, protocol=ProtocolType.SMB, service_name="microsoft-ds")
        config = ExploitationConfig(timeout=30)

        # Mock socket operations
        mock_socket = Mock()
        mock_response = b'Windows Server 2008 SMB response data'

        with patch('socket.socket') as mock_socket_class, \
             patch('asyncio.to_thread') as mock_to_thread:
            
            mock_socket_class.return_value = mock_socket
            mock_to_thread.side_effect = [None, None, mock_response]  # connect, send, recv

            with patch.object(self.exploiter, '_is_vulnerable_to_eternalblue', return_value=True):
                result = await self.exploiter._test_eternalblue(target, config)

            assert result is True
            mock_socket.settimeout.assert_called_once_with(config.timeout)
            mock_socket.close.assert_called_once()

    @pytest.mark.asyncio
    async def test_eternalblue_detection_failure(self):
        """Test failed EternalBlue vulnerability detection"""
        target = ExploitTarget(host="*************", port=445, protocol=ProtocolType.SMB, service_name="microsoft-ds")
        config = ExploitationConfig(timeout=30)

        # Mock socket operations to raise exception
        with patch('socket.socket') as mock_socket_class, \
             patch('asyncio.to_thread', side_effect=ConnectionRefusedError("Connection refused")):
            
            result = await self.exploiter._test_eternalblue(target, config)

            assert result is False

    def test_is_vulnerable_to_eternalblue_positive(self):
        """Test EternalBlue vulnerability detection with vulnerable response"""
        vulnerable_responses = [
            b'Windows Server 2008 response',
            b'Windows 7 Professional',
            b'Windows Server 2012 R2',
            b'Windows 8.1 Enterprise'
        ]

        for response in vulnerable_responses:
            result = self.exploiter._is_vulnerable_to_eternalblue(response)
            assert result is True

    def test_is_vulnerable_to_eternalblue_negative(self):
        """Test EternalBlue vulnerability detection with non-vulnerable response"""
        non_vulnerable_responses = [
            b'Linux Samba server',
            b'Windows Server 2019',
            b'Random response data',
            b''
        ]

        for response in non_vulnerable_responses:
            result = self.exploiter._is_vulnerable_to_eternalblue(response)
            assert result is False

    @pytest.mark.asyncio
    async def test_null_session_detection_success(self):
        """Test successful null session detection"""
        target = ExploitTarget(host="*************", port=445, protocol=ProtocolType.SMB, service_name="microsoft-ds")
        config = ExploitationConfig(timeout=30)

        # Mock SMBConnection
        mock_connection = Mock()
        mock_shares = [{'shi1_netname': 'ADMIN$\x00'}, {'shi1_netname': 'C$\x00'}]

        with patch('src.exploits.smb_exploiter.IMPACKET_AVAILABLE', True), \
             patch('src.exploits.smb_exploiter.SMBConnection') as mock_smb_conn, \
             patch('asyncio.to_thread') as mock_to_thread:
            
            mock_smb_conn.return_value = mock_connection
            mock_to_thread.side_effect = [None, mock_shares]  # login, listShares

            result = await self.exploiter._test_null_session(target, config)

            assert result is True
            mock_connection.close.assert_called_once()

    @pytest.mark.asyncio
    async def test_null_session_detection_failure(self):
        """Test failed null session detection"""
        target = ExploitTarget(host="*************", port=445, protocol=ProtocolType.SMB, service_name="microsoft-ds")
        config = ExploitationConfig(timeout=30)

        with patch('src.exploits.smb_exploiter.IMPACKET_AVAILABLE', True), \
             patch('src.exploits.smb_exploiter.SMBConnection') as mock_smb_conn, \
             patch('asyncio.to_thread', side_effect=Exception("Access denied")):
            
            result = await self.exploiter._test_null_session(target, config)

            assert result is False

    @pytest.mark.asyncio
    async def test_smb_credential_attack_success(self):
        """Test successful SMB credential attack"""
        target = ExploitTarget(host="*************", port=445, protocol=ProtocolType.SMB, service_name="microsoft-ds")
        config = ExploitationConfig(mode=ExploitationMode.TARGETED, timeout=30)

        mock_connection = Mock()
        test_credentials = SMBCredentials('admin', 'password')

        with patch('src.exploits.smb_exploiter.IMPACKET_AVAILABLE', True), \
             patch.object(self.exploiter, '_try_smb_login', return_value=(True, mock_connection)), \
             patch.object(self.exploiter, '_detect_smb_version', return_value=SMBVersion.SMB2), \
             patch.object(self.exploiter, '_enumerate_shares', return_value=['C$', 'ADMIN$']):

            result = await self.exploiter._smb_credential_attack(target, config)

            assert result.success is True
            assert result.exploit_type == "smb_credentials"
            assert 'username' in result.credentials
            assert result.access_level == "user"

    @pytest.mark.asyncio
    async def test_smb_credential_attack_failure(self):
        """Test failed SMB credential attack"""
        target = ExploitTarget(host="*************", port=445, protocol=ProtocolType.SMB, service_name="microsoft-ds")
        config = ExploitationConfig(mode=ExploitationMode.TARGETED, timeout=30)

        with patch('src.exploits.smb_exploiter.IMPACKET_AVAILABLE', True), \
             patch.object(self.exploiter, '_try_smb_login', return_value=(False, None)):

            result = await self.exploiter._smb_credential_attack(target, config)

            assert result.success is False
            assert result.exploit_type == "smb_credentials"
            assert "SMB credential attack failed" in result.error_message

    @pytest.mark.asyncio
    async def test_try_smb_login_success(self):
        """Test successful SMB login"""
        target = ExploitTarget(host="*************", port=445, protocol=ProtocolType.SMB, service_name="microsoft-ds")
        credentials = SMBCredentials('admin', 'password', 'DOMAIN')
        config = ExploitationConfig(timeout=30)

        mock_connection = Mock()

        with patch('src.exploits.smb_exploiter.SMBConnection') as mock_smb_conn, \
             patch('asyncio.to_thread') as mock_to_thread:
            
            mock_smb_conn.return_value = mock_connection
            mock_to_thread.return_value = None  # Successful login

            success, conn = await self.exploiter._try_smb_login(target, credentials, config)

            assert success is True
            assert conn == mock_connection
            mock_to_thread.assert_called_once_with(
                mock_connection.login, 'admin', 'password', 'DOMAIN'
            )

    @pytest.mark.asyncio
    async def test_try_smb_login_failure(self):
        """Test failed SMB login"""
        target = ExploitTarget(host="*************", port=445, protocol=ProtocolType.SMB, service_name="microsoft-ds")
        credentials = SMBCredentials('admin', 'wrongpassword')
        config = ExploitationConfig(timeout=30)

        with patch('src.exploits.smb_exploiter.SMBConnection') as mock_smb_conn, \
             patch('asyncio.to_thread', side_effect=Exception("Login failed")):
            
            success, conn = await self.exploiter._try_smb_login(target, credentials, config)

            assert success is False
            assert conn is None

    @pytest.mark.asyncio
    async def test_enumerate_shares_success(self):
        """Test successful share enumeration"""
        mock_connection = Mock()
        mock_shares = [
            {'shi1_netname': 'C$\x00'},
            {'shi1_netname': 'ADMIN$\x00'},
            {'shi1_netname': 'IPC$\x00'}
        ]

        with patch('asyncio.to_thread', return_value=mock_shares):
            shares = await self.exploiter._enumerate_shares(mock_connection)

            assert len(shares) == 3
            assert 'C$' in shares
            assert 'ADMIN$' in shares
            assert 'IPC$' in shares

    @pytest.mark.asyncio
    async def test_enumerate_shares_failure(self):
        """Test failed share enumeration"""
        mock_connection = Mock()

        with patch('asyncio.to_thread', side_effect=Exception("Access denied")):
            shares = await self.exploiter._enumerate_shares(mock_connection)

            assert shares == []

    @pytest.mark.asyncio
    async def test_lateral_movement_success(self):
        """Test successful lateral movement"""
        # Setup initial session
        target = ExploitTarget(host="*************", port=445, protocol=ProtocolType.SMB, service_name="microsoft-ds")
        mock_connection = Mock()
        credentials = SMBCredentials('admin', 'password')
        session = SMBSession(
            target=target,
            connection=mock_connection,
            credentials=credentials,
            smb_version=SMBVersion.SMB2,
            shares=['C$', 'ADMIN$']
        )

        session_key = "*************:445"
        self.exploiter.active_sessions[session_key] = session

        target_hosts = ["*************", "*************"]
        mock_new_connection = Mock()

        with patch.object(self.exploiter, '_try_smb_login', return_value=(True, mock_new_connection)), \
             patch.object(self.exploiter, '_enumerate_shares', return_value=['C$', 'ADMIN$']), \
             patch.object(self.exploiter, '_detect_smb_version', return_value=SMBVersion.SMB2):

            results = await self.exploiter.lateral_movement(session_key, target_hosts)

            assert len(results) == 2
            for result in results:
                assert result.success is True
                assert result.exploit_type == "smb_lateral_movement"
                assert 'username' in result.credentials

    @pytest.mark.asyncio
    async def test_lateral_movement_no_session(self):
        """Test lateral movement with no active session"""
        results = await self.exploiter.lateral_movement("nonexistent:445", ["*************"])
        assert results == []

    def test_get_active_sessions(self):
        """Test getting active session information"""
        target = ExploitTarget(host="*************", port=445, protocol=ProtocolType.SMB, service_name="microsoft-ds")
        mock_connection = Mock()
        credentials = SMBCredentials('admin', 'password', 'DOMAIN')
        session = SMBSession(
            target=target,
            connection=mock_connection,
            credentials=credentials,
            smb_version=SMBVersion.SMB2,
            shares=['C$', 'ADMIN$']
        )

        session_key = "*************:445"
        self.exploiter.active_sessions[session_key] = session

        sessions_info = self.exploiter.get_active_sessions()

        assert session_key in sessions_info
        session_data = sessions_info[session_key]
        assert session_data['target']['host'] == "*************"
        assert session_data['credentials']['username'] == "admin"
        assert session_data['smb_version'] == "SMBv2"
        assert 'C$' in session_data['shares']

    def test_close_session_success(self):
        """Test successful session closure"""
        target = ExploitTarget(host="*************", port=445, protocol=ProtocolType.SMB, service_name="microsoft-ds")
        mock_connection = Mock()
        credentials = SMBCredentials('admin', 'password')
        session = SMBSession(
            target=target,
            connection=mock_connection,
            credentials=credentials,
            smb_version=SMBVersion.SMB2
        )

        session_key = "*************:445"
        self.exploiter.active_sessions[session_key] = session

        result = self.exploiter.close_session(session_key)

        assert result is True
        assert session_key not in self.exploiter.active_sessions
        mock_connection.close.assert_called_once()

    def test_close_session_nonexistent(self):
        """Test closing non-existent session"""
        result = self.exploiter.close_session("nonexistent:445")
        assert result is False

    @pytest.mark.asyncio
    async def test_access_file_system_success(self):
        """Test successful file system access"""
        target = ExploitTarget(host="*************", port=445, protocol=ProtocolType.SMB, service_name="microsoft-ds")
        mock_connection = Mock()
        credentials = SMBCredentials('admin', 'password')
        session = SMBSession(
            target=target,
            connection=mock_connection,
            credentials=credentials,
            smb_version=SMBVersion.SMB2
        )

        session_key = "*************:445"
        self.exploiter.active_sessions[session_key] = session

        # Mock file listing
        mock_file_info = Mock()
        mock_file_info.get_longname.return_value = "test.txt"
        mock_file_info.get_filesize.return_value = 1024
        mock_file_info.is_directory.return_value = False
        mock_file_info.get_ctime_epoch.return_value = 1640995200
        mock_file_info.get_mtime_epoch.return_value = 1640995200

        with patch('asyncio.to_thread', return_value=[mock_file_info]):
            result = await self.exploiter.access_file_system(session_key, "C$", "/")

            assert result is not None
            assert len(result) == 1
            assert result[0]['name'] == "test.txt"
            assert result[0]['size'] == 1024
            assert result[0]['is_directory'] is False

    @pytest.mark.asyncio
    async def test_access_file_system_no_session(self):
        """Test file system access with no active session"""
        result = await self.exploiter.access_file_system("nonexistent:445", "C$", "/")
        assert result is None

    @pytest.mark.asyncio
    async def test_download_file_success(self):
        """Test successful file download"""
        target = ExploitTarget(host="*************", port=445, protocol=ProtocolType.SMB, service_name="microsoft-ds")
        mock_connection = Mock()
        credentials = SMBCredentials('admin', 'password')
        session = SMBSession(
            target=target,
            connection=mock_connection,
            credentials=credentials,
            smb_version=SMBVersion.SMB2
        )

        session_key = "*************:445"
        self.exploiter.active_sessions[session_key] = session

        with patch('builtins.open', mock_open()) as mock_file, \
             patch('asyncio.to_thread') as mock_to_thread:

            mock_to_thread.return_value = None  # Successful download

            result = await self.exploiter.download_file(session_key, "C$", "/test.txt", "/tmp/test.txt")

            assert result is True
            mock_file.assert_called_once_with("/tmp/test.txt", 'wb')

    @pytest.mark.asyncio
    async def test_download_file_no_session(self):
        """Test file download with no active session"""
        result = await self.exploiter.download_file("nonexistent:445", "C$", "/test.txt", "/tmp/test.txt")
        assert result is False


class TestSMBCredentials:
    """Test SMB credentials class"""

    def test_smb_credentials_creation(self):
        """Test SMB credentials creation"""
        creds = SMBCredentials(
            username="admin",
            password="password",
            domain="DOMAIN",
            ntlm_hash="hash123",
            lm_hash="lmhash123"
        )

        assert creds.username == "admin"
        assert creds.password == "password"
        assert creds.domain == "DOMAIN"
        assert creds.ntlm_hash == "hash123"
        assert creds.lm_hash == "lmhash123"

    def test_smb_credentials_minimal(self):
        """Test SMB credentials with minimal data"""
        creds = SMBCredentials(username="guest")

        assert creds.username == "guest"
        assert creds.password is None
        assert creds.domain is None
        assert creds.ntlm_hash is None
        assert creds.lm_hash is None


class TestSMBEnums:
    """Test SMB enumeration classes"""

    def test_smb_version_enum(self):
        """Test SMB version enumeration"""
        assert SMBVersion.SMB1.value == "SMBv1"
        assert SMBVersion.SMB2.value == "SMBv2"
        assert SMBVersion.SMB3.value == "SMBv3"

    def test_smb_vulnerability_enum(self):
        """Test SMB vulnerability enumeration"""
        assert SMBVulnerability.ETERNALBLUE.value == "ms17-010"
        assert SMBVulnerability.CONFICKER.value == "ms08-067"
        assert SMBVulnerability.NETAPI.value == "ms06-040"
